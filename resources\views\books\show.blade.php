<x-layouts.app>
    <x-slot name="title">{{ $book->title }} - Free Download | Zajel Arabic Academy</x-slot>
    <x-slot name="metaDescription">{{ $book->meta_description ?: Str::limit($book->description, 160) }}</x-slot>

    <!-- Book Hero Section -->
    <section class="bg-gradient-primary text-white py-16 md:py-20">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Book Info -->
                <div>
                    <!-- Breadcrumb -->
                    <nav class="flex items-center space-x-2 text-blue-200 mb-6">
                        <a href="/" class="hover:text-white transition-colors font-medium">Home</a>
                        <i class="fas fa-chevron-right text-xs"></i>
                        <a href="/books" class="hover:text-white transition-colors font-medium">Books</a>
                        <i class="fas fa-chevron-right text-xs"></i>
                        <span class="text-white font-medium">{{ Str::limit($book->title, 30) }}</span>
                    </nav>

                    <h1 class="text-4xl md:text-5xl font-bold mb-4 leading-tight">
                        {{ $book->title }}
                    </h1>

                    <p class="text-xl text-blue-100 mb-6 leading-relaxed font-medium">
                        by {{ $book->author }}
                    </p>

                    <p class="text-lg text-blue-100 mb-8 leading-relaxed font-medium">
                        {{ $book->description }}
                    </p>

                    <!-- Book Meta -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                        @if($book->pages)
                            <div class="text-center">
                                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-file-alt text-lg"></i>
                                </div>
                                <div class="text-sm text-blue-200 font-medium">Pages</div>
                                <div class="font-semibold">{{ $book->pages }}</div>
                            </div>
                        @endif

                        @if($book->language)
                            <div class="text-center">
                                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-language text-lg"></i>
                                </div>
                                <div class="text-sm text-blue-200 font-medium">Language</div>
                                <div class="font-semibold">{{ $book->language }}</div>
                            </div>
                        @endif

                        @if($book->file_size)
                            <div class="text-center">
                                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-download text-lg"></i>
                                </div>
                                <div class="text-sm text-blue-200 font-medium">File Size</div>
                                <div class="font-semibold">{{ $book->file_size }}</div>
                            </div>
                        @endif

                        <div class="text-center">
                            <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-users text-lg"></i>
                            </div>
                            <div class="text-sm text-blue-200 font-medium">Downloads</div>
                            <div class="font-semibold">{{ number_format($book->download_count) }}</div>
                        </div>
                    </div>

                    <!-- Download Button -->
                    @if($book->file_path)
                        <a href="/books/{{ $book->slug }}/download" class="inline-flex items-center justify-center bg-white text-blue-600 hover:bg-gray-50 hover:text-blue-700 text-lg font-bold px-8 py-4 rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-200">
                            <i class="fas fa-download mr-3"></i>
                            @if($book->is_free)
                                Download Free
                            @else
                                Buy & Download - ${{ number_format($book->price, 0) }}
                            @endif
                        </a>
                    @else
                        <div class="inline-flex items-center justify-center bg-gray-400 text-white text-lg font-bold px-8 py-4 rounded-xl cursor-not-allowed">
                            <i class="fas fa-clock mr-3"></i>
                            Coming Soon
                        </div>
                    @endif
                </div>

                <!-- Book Cover -->
                <div class="relative">
                    @if($book->image)
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl">
                            <img
                                src="{{ Storage::url($book->image) }}"
                                alt="{{ $book->title }}"
                                class="w-full h-96 md:h-[500px] object-cover"
                            >
                        </div>
                    @else
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl bg-white/10 backdrop-blur-sm border border-white/20">
                            <div class="flex items-center justify-center h-96 md:h-[500px]">
                                <div class="text-center text-white">
                                    <i class="fas fa-book text-6xl mb-4 opacity-80"></i>
                                    <p class="text-xl font-semibold">{{ $book->title }}</p>
                                    <p class="text-blue-200 mt-2">by {{ $book->author }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Price Badge -->
                    <div class="absolute top-4 right-4">
                        @if($book->is_free)
                            <div class="bg-green-500 text-white px-4 py-2 rounded-full font-bold shadow-lg">
                                <span class="text-lg">Free</span>
                            </div>
                        @else
                            <div class="bg-primary-600 text-white px-4 py-2 rounded-full font-bold shadow-lg">
                                <span class="text-lg">${{ number_format($book->price, 0) }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Book Details -->
    <section class="py-20 bg-white">
        <div class="max-w-4xl mx-auto container-padding">
            <!-- Book Description -->
            <div class="mb-20">
                <div class="text-center mb-12">
                    <h2 class="text-4xl font-bold text-gray-900 mb-4">About This Book</h2>
                    <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-blue-700 mx-auto rounded-full"></div>
                </div>
                <div class="prose prose-xl max-w-none text-gray-700 leading-relaxed font-medium bg-gradient-to-br from-gray-50 to-blue-50 rounded-3xl p-12 shadow-lg border border-gray-100">
                    {!! nl2br(e($book->description)) !!}
                </div>
            </div>

            <!-- Book Details Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
                <!-- Book Information -->
                <div class="card p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Book Information</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600 font-medium">Title:</span>
                            <span class="text-gray-900 font-semibold">{{ $book->title }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 font-medium">Author:</span>
                            <span class="text-gray-900 font-semibold">{{ $book->author }}</span>
                        </div>
                        @if($book->isbn)
                            <div class="flex justify-between">
                                <span class="text-gray-600 font-medium">ISBN:</span>
                                <span class="text-gray-900 font-semibold">{{ $book->isbn }}</span>
                            </div>
                        @endif
                        @if($book->publication_date)
                            <div class="flex justify-between">
                                <span class="text-gray-600 font-medium">Published:</span>
                                <span class="text-gray-900 font-semibold">{{ $book->publication_date->format('Y') }}</span>
                            </div>
                        @endif
                        @if($book->pages)
                            <div class="flex justify-between">
                                <span class="text-gray-600 font-medium">Pages:</span>
                                <span class="text-gray-900 font-semibold">{{ $book->pages }}</span>
                            </div>
                        @endif
                        <div class="flex justify-between">
                            <span class="text-gray-600 font-medium">Language:</span>
                            <span class="text-gray-900 font-semibold">{{ $book->language }}</span>
                        </div>
                        @if($book->file_size)
                            <div class="flex justify-between">
                                <span class="text-gray-600 font-medium">File Size:</span>
                                <span class="text-gray-900 font-semibold">{{ $book->file_size }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Download Stats -->
                <div class="card p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Download Information</h3>
                    <div class="space-y-4">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-primary-600 mb-2">{{ number_format($book->download_count) }}</div>
                            <div class="text-gray-600 font-medium">Total Downloads</div>
                        </div>

                        @if($book->is_free)
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                                <i class="fas fa-gift text-green-600 text-2xl mb-2"></i>
                                <div class="text-green-800 font-semibold">Free Download</div>
                                <div class="text-green-600 text-sm font-medium">No registration required</div>
                            </div>
                        @else
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                                <i class="fas fa-star text-blue-600 text-2xl mb-2"></i>
                                <div class="text-blue-800 font-semibold">Premium Content</div>
                                <div class="text-blue-600 text-sm font-medium">${{ number_format($book->price, 0) }} - One-time purchase</div>
                            </div>
                        @endif

                        @if($book->file_path)
                            <a href="/books/{{ $book->slug }}/download" class="block w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white text-center font-bold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-blue-800 transform hover:scale-105 transition-all duration-200">
                                <i class="fas fa-download mr-2"></i>
                                @if($book->is_free)
                                    Download Free
                                @else
                                    Buy & Download
                                @endif
                            </a>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Share Section -->
            <div class="border-t border-gray-200 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Share This Book</h3>
                        <p class="text-gray-600 font-medium">Help others discover this valuable resource</p>
                    </div>

                    <div class="flex items-center space-x-3">
                        <a
                            href="https://twitter.com/intent/tweet?text={{ urlencode($book->title . ' by ' . $book->author) }}&url={{ urlencode(url()->current()) }}"
                            target="_blank"
                            class="w-10 h-10 bg-blue-500 hover:bg-blue-600 text-white rounded-full flex items-center justify-center transition-colors duration-200"
                            aria-label="Share on Twitter"
                        >
                            <i class="fab fa-twitter text-sm"></i>
                        </a>
                        <a
                            href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(url()->current()) }}"
                            target="_blank"
                            class="w-10 h-10 bg-blue-600 hover:bg-blue-700 text-white rounded-full flex items-center justify-center transition-colors duration-200"
                            aria-label="Share on Facebook"
                        >
                            <i class="fab fa-facebook-f text-sm"></i>
                        </a>
                        <a
                            href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(url()->current()) }}"
                            target="_blank"
                            class="w-10 h-10 bg-blue-700 hover:bg-blue-800 text-white rounded-full flex items-center justify-center transition-colors duration-200"
                            aria-label="Share on LinkedIn"
                        >
                            <i class="fab fa-linkedin-in text-sm"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Books -->
    @if($relatedBooks->count() > 0)
    <section class="py-20 bg-gray-50 mt-16">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    Related Books
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
                    Discover more books by {{ $book->author }} and similar topics.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                @foreach($relatedBooks as $relatedBook)
                    <div class="card card-hover group">
                        <!-- Book Cover -->
                        <div class="relative overflow-hidden">
                            @if($relatedBook->image)
                                <img
                                    src="{{ Storage::url($relatedBook->image) }}"
                                    alt="{{ $relatedBook->title }}"
                                    class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                >
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                    <i class="fas fa-book text-gray-400 text-3xl"></i>
                                </div>
                            @endif

                            <!-- Price Badge -->
                            <div class="absolute top-4 right-4">
                                @if($relatedBook->is_free)
                                    <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold">
                                        Free
                                    </span>
                                @else
                                    <span class="bg-primary-600 text-white px-2 py-1 rounded text-xs font-bold">
                                        ${{ number_format($relatedBook->price, 0) }}
                                    </span>
                                @endif
                            </div>
                        </div>

                        <!-- Book Content -->
                        <div class="p-4">
                            <h3 class="text-lg font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
                                {{ Str::limit($relatedBook->title, 40) }}
                            </h3>
                            <p class="text-gray-600 mb-3 font-medium text-sm">by {{ $relatedBook->author }}</p>
                            <a href="/books/{{ $relatedBook->slug }}" class="block w-full btn btn-outline text-center font-semibold text-sm">
                                <i class="fas fa-eye mr-2"></i>
                                View Details
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-primary mt-16">
        <div class="max-w-4xl mx-auto container-padding text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Want to Learn More?
            </h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed font-medium">
                Complement your reading with our interactive courses and personalized instruction.
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/courses" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                    <i class="fas fa-graduation-cap mr-3"></i>
                    Browse Courses
                </a>
                <a href="/trial-class" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 text-lg font-semibold px-8 py-4">
                    <i class="fas fa-play-circle mr-3"></i>
                    Try Free Class
                </a>
            </div>
        </div>
    </section>
</x-layouts.app>
