<?php

namespace App\Http\Controllers;

use App\Models\PricingPlan;

class PricingController extends Controller
{
    public function index()
    {
        // Get pricing plans grouped by duration type
        $thirtyMinutePlans = PricingPlan::active()
            ->ordered()
            ->where('sort_order', '<=', 4)
            ->get();

        $sixtyMinutePlans = PricingPlan::active()
            ->ordered()
            ->where('sort_order', '>', 4)
            ->get();

        // FAQ data
        $faqs = collect([
            (object) [
                'question' => 'Can I change my plan anytime?',
                'answer' => 'Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.'
            ],
            (object) [
                'question' => 'Is there a free trial available?',
                'answer' => 'Absolutely! All plans come with a free trial class so you can experience our teaching quality before committing.'
            ],
            (object) [
                'question' => 'What if I need to reschedule a lesson?',
                'answer' => 'Our 60-minute plans include lesson rescheduling. For 30-minute plans, please contact support for assistance.'
            ],
            (object) [
                'question' => 'Are the materials really free?',
                'answer' => 'Yes! All learning materials, including books, worksheets, and digital resources are included at no extra cost.'
            ]
        ]);

        return view('pricing', compact('thirtyMinutePlans', 'sixtyMinutePlans', 'faqs'));
    }
}
