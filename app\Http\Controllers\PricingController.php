<?php

namespace App\Http\Controllers;

use App\Models\PricingPlan;
use Illuminate\Http\Request;

class PricingController extends Controller
{
    public function index()
    {
        // Get pricing plans grouped by duration type
        $thirtyMinutePlans = PricingPlan::active()
            ->ordered()
            ->where('sort_order', '<=', 4)
            ->get();
            
        $sixtyMinutePlans = PricingPlan::active()
            ->ordered()
            ->where('sort_order', '>', 4)
            ->get();

        return view('pricing', compact('thirtyMinutePlans', 'sixtyMinutePlans'));
    }
}
