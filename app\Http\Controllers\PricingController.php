<?php

namespace App\Http\Controllers;

use App\Models\PricingPlan;
use Illuminate\Http\Request;

class PricingController extends Controller
{
    public function index()
    {
        // Get pricing plans grouped by duration type
        $thirtyMinutePlans = PricingPlan::active()
            ->ordered()
            ->where('sort_order', '<=', 4)
            ->get()
            ->map(function ($plan) {
                // Ensure features are in the correct format for display
                $features = $plan->features;
                if (is_array($features) && isset($features[0]['feature'])) {
                    $plan->features = collect($features)->pluck('feature')->toArray();
                }
                return $plan;
            });

        $sixtyMinutePlans = PricingPlan::active()
            ->ordered()
            ->where('sort_order', '>', 4)
            ->get()
            ->map(function ($plan) {
                // Ensure features are in the correct format for display
                $features = $plan->features;
                if (is_array($features) && isset($features[0]['feature'])) {
                    $plan->features = collect($features)->pluck('feature')->toArray();
                }
                return $plan;
            });

        return view('pricing', compact('thirtyMinutePlans', 'sixtyMinutePlans'));
    }
}
