<?php if (isset($component)) { $__componentOriginal5863877a5171c196453bfa0bd807e410 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5863877a5171c196453bfa0bd807e410 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.app','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> Frequently Asked Questions - Zajel Arabic Academy <?php $__env->endSlot(); ?>
     <?php $__env->slot('metaDescription', null, []); ?> Find answers to common questions about our Arabic and Quran courses, enrollment process, pricing, and learning methods at Zajel Arabic Academy. <?php $__env->endSlot(); ?>

    <!-- Page Header -->
    <section class="bg-gradient-primary text-white py-16 md:py-20">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                    Frequently Asked Questions
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed font-medium">
                    Find answers to common questions about our courses, enrollment, and learning process.
                </p>
            </div>
        </div>
    </section>

    <!-- FAQ Content -->
    <section class="section-padding bg-white">
        <div class="max-w-4xl mx-auto container-padding">
            <div class="space-y-8">
                <?php $__currentLoopData = $faqs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="card p-6">
                        <button 
                            class="w-full text-left flex items-center justify-between focus:outline-none"
                            onclick="toggleFaq(<?php echo e($index); ?>)"
                        >
                            <h3 class="text-lg md:text-xl font-bold text-gray-900 pr-4">
                                <?php echo e($faq['question']); ?>

                            </h3>
                            <i id="icon-<?php echo e($index); ?>" class="fas fa-chevron-down text-primary-600 transition-transform duration-200"></i>
                        </button>
                        <div id="answer-<?php echo e($index); ?>" class="hidden mt-4 pt-4 border-t border-gray-200">
                            <p class="text-gray-700 leading-relaxed font-medium">
                                <?php echo e($faq['answer']); ?>

                            </p>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Additional FAQ Categories -->
            <div class="mt-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">More Questions?</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- General Questions -->
                    <div class="text-center">
                        <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-question-circle text-primary-600 text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">General Questions</h3>
                        <p class="text-gray-600 mb-4 font-medium">
                            Questions about our academy, teaching methods, and general information.
                        </p>
                        <a href="/contact" class="text-primary-600 hover:text-primary-700 font-semibold">
                            Contact Us →
                        </a>
                    </div>

                    <!-- Technical Support -->
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-headset text-green-600 text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">Technical Support</h3>
                        <p class="text-gray-600 mb-4 font-medium">
                            Need help with platform access, video calls, or technical issues?
                        </p>
                        <a href="/contact" class="text-primary-600 hover:text-primary-700 font-semibold">
                            Get Help →
                        </a>
                    </div>

                    <!-- Course Specific -->
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-graduation-cap text-blue-600 text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">Course Questions</h3>
                        <p class="text-gray-600 mb-4 font-medium">
                            Specific questions about course content, curriculum, and learning outcomes.
                        </p>
                        <a href="/courses" class="text-primary-600 hover:text-primary-700 font-semibold">
                            Browse Courses →
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section-padding bg-gradient-primary">
        <div class="max-w-4xl mx-auto container-padding text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Still Have Questions?
            </h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed font-medium">
                Our team is here to help! Contact us directly or start with a free trial class to experience our teaching firsthand.
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/contact" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                    <i class="fas fa-envelope mr-3"></i>
                    Contact Us
                </a>
                <a href="/trial-class" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 text-lg font-semibold px-8 py-4">
                    <i class="fas fa-play-circle mr-3"></i>
                    Try Free Class
                </a>
            </div>
        </div>
    </section>

    <!-- FAQ JavaScript -->
    <script>
        function toggleFaq(index) {
            const answer = document.getElementById(`answer-${index}`);
            const icon = document.getElementById(`icon-${index}`);
            
            if (answer.classList.contains('hidden')) {
                answer.classList.remove('hidden');
                icon.classList.add('rotate-180');
            } else {
                answer.classList.add('hidden');
                icon.classList.remove('rotate-180');
            }
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $attributes = $__attributesOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__attributesOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $component = $__componentOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__componentOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\zajelwebsite\resources\views/pages/faq.blade.php ENDPATH**/ ?>