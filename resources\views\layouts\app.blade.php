<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="scroll-smooth">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title ?? config('app.name', 'Zajel Arabic Academy') }}</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="{{ $metaDescription ?? 'Learn Arabic language, Quran memorization, and recitation with expert instructors. Online courses for non-Arabic speakers worldwide.' }}">
    <meta name="keywords" content="{{ $metaKeywords ?? 'Arabic learning, Quran memorization, Quran recitation, online Arabic courses, Islamic education' }}">
    <meta name="author" content="Zajel Arabic Academy">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{ $title ?? config('app.name') }}">
    <meta property="og:description" content="{{ $metaDescription ?? 'Learn Arabic language, Quran memorization, and recitation with expert instructors.' }}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:image" content="{{ $ogImage ?? asset('images/og-default.jpg') }}">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $title ?? config('app.name') }}">
    <meta name="twitter:description" content="{{ $metaDescription ?? 'Learn Arabic language, Quran memorization, and recitation with expert instructors.' }}">
    <meta name="twitter:image" content="{{ $ogImage ?? asset('images/og-default.jpg') }}">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('apple-touch-icon.png') }}">

    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- Tailwind CSS CDN (temporary solution) -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        accent: {
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'Roboto', 'Noto Sans Arabic', 'Cairo', 'sans-serif'],
                    },
                    backgroundImage: {
                        'gradient-primary': 'linear-gradient(135deg, #1e40af 0%, #2563eb 100%)',
                    }
                }
            }
        }
    </script>

    <!-- Scripts -->
    @if (file_exists(public_path('build/manifest.json')))
        @vite(['resources/js/app.js'])
    @endif

    <!-- Forms JavaScript -->
    <script src="{{ asset('js/forms.js') }}"></script>

    <!-- Page-specific scripts -->
    @stack('scripts')


    <!-- Header & Navigation CSS -->
    <style>
        /* Base Styles */
        body {
            font-family: 'Inter', 'Roboto', 'Noto Sans Arabic', 'Cairo', sans-serif;
            line-height: 1.6;
        }

        /* Navigation Active States */
        header .nav-link {
            position: relative !important;
            color: #374151 !important;
            text-decoration: none !important;
            padding: 0.5rem 0 !important;
            transition: all 0.3s ease !important;
            display: inline-block !important;
        }

        header .nav-link:hover {
            color: #3b82f6 !important;
        }

        header .nav-link.active {
            color: #3b82f6 !important;
            font-weight: 600 !important;
        }

        header .nav-link.active::after {
            content: '' !important;
            position: absolute !important;
            bottom: -8px !important;
            left: 0 !important;
            right: 0 !important;
            height: 3px !important;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8) !important;
            border-radius: 2px !important;
        }

        /* Dropdown Styles */
        .group:hover .group-hover\:opacity-100 {
            opacity: 1 !important;
        }

        .group:hover .group-hover\:visible {
            visibility: visible !important;
        }

        .group:hover .group-hover\:rotate-180 {
            transform: rotate(180deg) !important;
        }

        /* Mobile Menu Styles */
        #mobile-overlay {
            backdrop-filter: blur(2px) !important;
        }

        .rotate-180 {
            transform: rotate(180deg) !important;
        }

        /* Mobile Menu Animation */
        #mobile-menu {
            transition: transform 0.3s ease-in-out !important;
        }

        #mobile-menu.open {
            transform: translateX(0) !important;
        }

        #mobile-overlay.show {
            opacity: 1 !important;
            visibility: visible !important;
        }
    </style>

    <!-- Additional Head Content -->
    @stack('head')
</head>
<body class="font-sans antialiased bg-white text-gray-900">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>

    <!-- Header -->
    @include('layouts.partials.header')

    <!-- Main Content -->
    <main id="main-content" class="min-h-screen">
        {{ $slot }}
    </main>

    <!-- Footer -->
    @include('layouts.partials.footer')

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>

    <!-- Back to Top Button -->
    <button id="back-to-top" class="fixed bottom-6 right-6 bg-primary-600 hover:bg-primary-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 opacity-0 invisible z-30">
        <i class="fas fa-arrow-up text-sm"></i>
    </button>

    <!-- Scripts -->
    <script>
        // Set active navigation state
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Setting up navigation active states...');
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('header .nav-link');

            console.log('Current path:', currentPath);
            console.log('Found nav links:', navLinks.length);

            navLinks.forEach(link => {
                link.classList.remove('active');
                const href = link.getAttribute('href');

                // Exact match for active state
                if (href === currentPath) {
                    link.classList.add('active');
                    console.log('Activated link:', href);
                } else if (currentPath === '/' && href === '/') {
                    link.classList.add('active');
                    console.log('Activated home link');
                } else if (currentPath === '/pricing' && href === '/pricing') {
                    link.classList.add('active');
                    console.log('Activated pricing link');
                } else if (currentPath.startsWith('/courses') && href === '/courses') {
                    link.classList.add('active');
                    console.log('Activated courses link');
                } else if (currentPath.startsWith('/blog') && href === '/blog') {
                    link.classList.add('active');
                    console.log('Activated blog link');
                } else if (currentPath.startsWith('/books') && href === '/books') {
                    link.classList.add('active');
                    console.log('Activated books link');
                } else if (currentPath.startsWith('/about') && href === '/about') {
                    link.classList.add('active');
                    console.log('Activated about link');
                } else if (currentPath.startsWith('/contact') && href === '/contact') {
                    link.classList.add('active');
                    console.log('Activated contact link');
                }
            });

            console.log('Navigation setup complete');
        });

        // Mobile Menu Functions - Make them global
        window.toggleMobileMenu = function() {
            console.log('Toggle mobile menu called');
            const menu = document.getElementById('mobile-menu');
            const overlay = document.getElementById('mobile-overlay');

            if (!menu || !overlay) {
                console.log('Menu or overlay not found');
                console.log('Menu:', menu);
                console.log('Overlay:', overlay);
                return;
            }

            if (menu.classList.contains('-translate-x-full')) {
                openMobileMenu();
            } else {
                closeMobileMenu();
            }
        };

        window.openMobileMenu = function() {
            console.log('Opening mobile menu');
            const menu = document.getElementById('mobile-menu');
            const overlay = document.getElementById('mobile-overlay');

            if (menu && overlay) {
                menu.classList.remove('-translate-x-full');
                menu.classList.add('translate-x-0');
                overlay.classList.remove('opacity-0', 'invisible');
                overlay.classList.add('opacity-100', 'visible');
                document.body.style.overflow = 'hidden';
                console.log('Mobile menu opened');
            }
        };

        window.closeMobileMenu = function() {
            console.log('Closing mobile menu');
            const menu = document.getElementById('mobile-menu');
            const overlay = document.getElementById('mobile-overlay');

            if (menu && overlay) {
                menu.classList.add('-translate-x-full');
                menu.classList.remove('translate-x-0');
                overlay.classList.add('opacity-0', 'invisible');
                overlay.classList.remove('opacity-100', 'visible');
                document.body.style.overflow = '';
                console.log('Mobile menu closed');
            }
        };

        // Toggle mobile courses dropdown
        window.toggleMobileCourses = function() {
            const dropdown = document.getElementById('mobile-courses-dropdown');
            const arrow = document.getElementById('courses-arrow');

            if (dropdown && dropdown.classList.contains('hidden')) {
                dropdown.classList.remove('hidden');
                if (arrow) arrow.classList.add('rotate-180');
            } else if (dropdown) {
                dropdown.classList.add('hidden');
                if (arrow) arrow.classList.remove('rotate-180');
            }
        };

        // Toggle mobile plans dropdown
        window.toggleMobilePlans = function() {
            const dropdown = document.getElementById('mobile-plans-dropdown');
            const arrow = document.getElementById('plans-arrow');

            if (dropdown && dropdown.classList.contains('hidden')) {
                dropdown.classList.remove('hidden');
                if (arrow) arrow.classList.add('rotate-180');
            } else if (dropdown) {
                dropdown.classList.add('hidden');
                if (arrow) arrow.classList.remove('rotate-180');
            }
        };

        // Toggle mobile account dropdown
        window.toggleMobileAccount = function() {
            const dropdown = document.getElementById('mobile-account-dropdown');
            const arrow = document.getElementById('account-arrow');

            if (dropdown && dropdown.classList.contains('hidden')) {
                dropdown.classList.remove('hidden');
                if (arrow) arrow.classList.add('rotate-180');
            } else if (dropdown) {
                dropdown.classList.add('hidden');
                if (arrow) arrow.classList.remove('rotate-180');
            }
        };

        // Setup mobile menu after DOM loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Setting up mobile menu...');

            // Check if elements exist
            const menu = document.getElementById('mobile-menu');
            const overlay = document.getElementById('mobile-overlay');
            const button = document.querySelector('[onclick="toggleMobileMenu()"]');

            console.log('Mobile menu element:', menu);
            console.log('Mobile overlay element:', overlay);
            console.log('Mobile button element:', button);

            // Close menu when clicking overlay
            if (overlay) {
                overlay.addEventListener('click', function() {
                    console.log('Overlay clicked');
                    closeMobileMenu();
                });
                console.log('Overlay click listener added');
            } else {
                console.warn('Mobile overlay not found');
            }
        });

        // Close menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const menu = document.getElementById('mobile-menu');
                if (menu && !menu.classList.contains('-translate-x-full')) {
                    closeMobileMenu();
                }
            }
        });

        // Back to top functionality
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('back-to-top');
            if (window.pageYOffset > 300) {
                backToTop.classList.remove('opacity-0', 'invisible');
                backToTop.classList.add('opacity-100', 'visible');
            } else {
                backToTop.classList.add('opacity-0', 'invisible');
                backToTop.classList.remove('opacity-100', 'visible');
            }
        });

        document.getElementById('back-to-top').addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Enhanced accessibility: Focus management
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const mobileMenu = document.getElementById('mobile-menu');
                if (!mobileMenu.classList.contains('translate-x-full')) {
                    toggleMobileMenu();
                }
            }
        });
    </script>

    <!-- Additional Scripts -->
    @stack('scripts')

    <!-- Simple Mobile Menu Test -->
    <script>
        // Simple test function
        function testMobileMenu() {
            console.log('Test function called');
            const menu = document.getElementById('mobile-menu');
            const overlay = document.getElementById('mobile-overlay');

            if (menu && overlay) {
                console.log('Elements found, toggling...');
                if (menu.style.transform === 'translateX(0px)') {
                    menu.style.transform = 'translateX(-100%)';
                    overlay.style.opacity = '0';
                    overlay.style.visibility = 'hidden';
                } else {
                    menu.style.transform = 'translateX(0px)';
                    overlay.style.opacity = '1';
                    overlay.style.visibility = 'visible';
                }
            } else {
                console.log('Elements not found');
                console.log('Menu:', menu);
                console.log('Overlay:', overlay);
            }
        }

        // Make it global for testing
        window.testMobileMenu = testMobileMenu;

        // Test on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, testing mobile menu elements...');
            testMobileMenu();
        });
    </script>
</body>
</html>
