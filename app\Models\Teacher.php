<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

class Teacher extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'title',
        'bio',
        'image',
        'email',
        'specializations',
        'languages',
        'experience_years',
        'education',
        'certifications',
        'teaching_approach',
        'availability',
        'rating',
        'total_students',
        'is_featured',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'specializations' => 'array',
        'languages' => 'array',
        'certifications' => 'array',
        'availability' => 'array',
        'rating' => 'decimal:2',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    // Accessors
    public function getImageUrlAttribute()
    {
        if ($this->image) {
            return Storage::url($this->image);
        }
        return asset('images/default-teacher.jpg');
    }

    public function getSpecializationsStringAttribute()
    {
        return implode(', ', $this->specializations ?? []);
    }

    public function getLanguagesStringAttribute()
    {
        return implode(', ', $this->languages ?? []);
    }

    public function getExperienceTextAttribute()
    {
        $years = $this->experience_years;
        if ($years == 1) {
            return '1 year of experience';
        }
        return $years . ' years of experience';
    }
}
