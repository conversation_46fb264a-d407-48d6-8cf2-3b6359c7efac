<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pricing_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "2 Days per Week"
            $table->decimal('price', 8, 2); // Current price
            $table->decimal('original_price', 8, 2)->nullable(); // Original price for discount
            $table->integer('discount_percentage')->default(0); // Discount percentage
            $table->string('duration')->default('per month'); // Duration text
            $table->json('features'); // Array of features
            $table->string('button_text')->default('Start Free Trial'); // Button text
            $table->string('button_url')->nullable(); // Button URL
            $table->boolean('is_popular')->default(false); // Popular badge
            $table->boolean('is_active')->default(true); // Active status
            $table->integer('sort_order')->default(0); // Sort order
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pricing_plans');
    }
};
