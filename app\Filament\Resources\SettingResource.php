<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SettingResource\Pages;
use App\Models\Setting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class SettingResource extends Resource
{
    protected static ?string $model = Setting::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationGroup = 'Settings';

    protected static ?int $navigationSort = 10;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Setting Information')
                    ->schema([
                        Forms\Components\TextInput::make('key')
                            ->required()
                            ->maxLength(255)
                            ->unique(Setting::class, 'key', ignoreRecord: true),
                        
                        Forms\Components\Select::make('type')
                            ->options([
                                'text' => 'Text',
                                'textarea' => 'Textarea',
                                'boolean' => 'Boolean',
                                'integer' => 'Integer',
                                'json' => 'JSON',
                                'image' => 'Image',
                            ])
                            ->required()
                            ->live(),
                        
                        Forms\Components\Select::make('group')
                            ->options([
                                'general' => 'General',
                                'home' => 'Home Page',
                                'about' => 'About',
                                'contact' => 'Contact',
                                'social' => 'Social Media',
                                'seo' => 'SEO',
                                'features' => 'Features',
                            ])
                            ->required(),
                        
                        Forms\Components\TextInput::make('description')
                            ->maxLength(255)
                            ->columnSpanFull(),
                    ])->columns(2),

                Forms\Components\Section::make('Setting Value')
                    ->schema([
                        Forms\Components\TextInput::make('value')
                            ->label('Value')
                            ->visible(fn (Forms\Get $get): bool => in_array($get('type'), ['text', 'integer']))
                            ->columnSpanFull(),
                        
                        Forms\Components\Textarea::make('value')
                            ->label('Value')
                            ->rows(4)
                            ->visible(fn (Forms\Get $get): bool => $get('type') === 'textarea')
                            ->columnSpanFull(),
                        
                        Forms\Components\Toggle::make('value')
                            ->label('Value')
                            ->visible(fn (Forms\Get $get): bool => $get('type') === 'boolean'),
                        
                        Forms\Components\FileUpload::make('value')
                            ->label('Image')
                            ->image()
                            ->directory('settings')
                            ->visible(fn (Forms\Get $get): bool => $get('type') === 'image'),
                        
                        Forms\Components\Textarea::make('value')
                            ->label('JSON Value')
                            ->rows(6)
                            ->visible(fn (Forms\Get $get): bool => $get('type') === 'json')
                            ->helperText('Enter valid JSON format')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('group')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'general' => 'gray',
                        'home' => 'primary',
                        'about' => 'info',
                        'contact' => 'success',
                        'social' => 'warning',
                        'seo' => 'danger',
                        'features' => 'purple',
                        default => 'gray',
                    }),
                
                Tables\Columns\TextColumn::make('type')
                    ->badge(),
                
                Tables\Columns\TextColumn::make('value')
                    ->limit(50)
                    ->formatStateUsing(function ($state, $record) {
                        if ($record->type === 'boolean') {
                            return $state ? 'Yes' : 'No';
                        }
                        if ($record->type === 'image' && $state) {
                            return 'Image uploaded';
                        }
                        return $state;
                    }),
                
                Tables\Columns\TextColumn::make('description')
                    ->limit(30)
                    ->toggleable(),
                
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('group')
                    ->options([
                        'general' => 'General',
                        'home' => 'Home Page',
                        'about' => 'About',
                        'contact' => 'Contact',
                        'social' => 'Social Media',
                        'seo' => 'SEO',
                        'features' => 'Features',
                    ]),
                
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'text' => 'Text',
                        'textarea' => 'Textarea',
                        'boolean' => 'Boolean',
                        'integer' => 'Integer',
                        'json' => 'JSON',
                        'image' => 'Image',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('group');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSettings::route('/'),
            'create' => Pages\CreateSetting::route('/create'),
            'edit' => Pages\EditSetting::route('/{record}/edit'),
        ];
    }
}
