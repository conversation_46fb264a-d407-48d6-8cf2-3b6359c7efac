<?php

namespace Database\Seeders;

use App\Models\Teacher;
use Illuminate\Database\Seeder;

class TeacherSeeder extends Seeder
{
    public function run(): void
    {
        $teachers = [
            [
                'name' => 'Dr. <PERSON>',
                'title' => 'Senior Arabic Language Instructor',
                'bio' => 'Dr. <PERSON> has over 15 years of experience teaching Arabic to non-native speakers. He holds a PhD in Arabic Literature from Al-Azhar University and has taught students from over 50 countries. His patient and methodical approach makes complex grammar concepts easy to understand.',
                'email' => '<EMAIL>',
                'specializations' => ['Arabic Language', 'Arabic Grammar', 'Conversational Arabic'],
                'languages' => ['Arabic', 'English', 'French'],
                'experience_years' => 15,
                'education' => 'PhD in Arabic Literature, Al-Azhar University',
                'certifications' => ['TESOL Certificate', 'Arabic Teaching Diploma'],
                'teaching_approach' => 'I believe in making Arabic learning enjoyable and practical. My lessons focus on real-life conversations while building a strong foundation in grammar and vocabulary.',
                'availability' => ['Monday' => 'Morning, Evening', 'Tuesday' => 'Morning', 'Wednesday' => 'Evening'],
                'rating' => 4.9,
                'total_students' => 450,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Ustadha <PERSON>ima Al-Zahra',
                'title' => 'Quran Recitation & Memorization Specialist',
                'bio' => 'Ustadha Fatima is a certified Quran teacher with Ijazah in multiple Qira\'at. She has helped hundreds of students memorize the Quran and perfect their recitation. Her gentle and encouraging teaching style is perfect for students of all ages.',
                'email' => '<EMAIL>',
                'specializations' => ['Quran Recitation', 'Quran Memorization', 'Tajweed'],
                'languages' => ['Arabic', 'English', 'Urdu'],
                'experience_years' => 12,
                'education' => 'Master in Quranic Studies, Islamic University of Medina',
                'certifications' => ['Ijazah in Hafs', 'Ijazah in Warsh', 'Tajweed Certificate'],
                'teaching_approach' => 'I focus on proper pronunciation and beautiful recitation. Each student learns at their own pace with personalized memorization techniques.',
                'availability' => ['Sunday' => 'All Day', 'Monday' => 'Morning', 'Thursday' => 'Evening'],
                'rating' => 5.0,
                'total_students' => 320,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Professor Omar Hassan',
                'title' => 'Islamic Studies & Arabic Literature Expert',
                'bio' => 'Professor Omar combines deep knowledge of Islamic studies with expertise in Arabic literature. He has authored several books on Islamic history and teaches with passion and scholarly precision.',
                'email' => '<EMAIL>',
                'specializations' => ['Islamic Studies', 'Arabic Literature', 'Islamic History'],
                'languages' => ['Arabic', 'English', 'German'],
                'experience_years' => 18,
                'education' => 'PhD in Islamic Studies, Cairo University',
                'certifications' => ['Islamic Studies Doctorate', 'Arabic Literature Certificate'],
                'teaching_approach' => 'I connect historical context with modern understanding, making Islamic studies relevant and engaging for contemporary students.',
                'availability' => ['Tuesday' => 'Evening', 'Wednesday' => 'Morning', 'Friday' => 'Morning'],
                'rating' => 4.8,
                'total_students' => 280,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Ustadh Yusuf Al-Maghribi',
                'title' => 'Conversational Arabic Specialist',
                'bio' => 'Ustadh Yusuf specializes in helping students speak Arabic confidently. With his interactive teaching methods and cultural insights, students quickly develop practical speaking skills.',
                'email' => '<EMAIL>',
                'specializations' => ['Conversational Arabic', 'Arabic Culture', 'Business Arabic'],
                'languages' => ['Arabic', 'English', 'Spanish'],
                'experience_years' => 10,
                'education' => 'Master in Applied Linguistics, Mohammed V University',
                'certifications' => ['TESOL Certificate', 'Business Arabic Certificate'],
                'teaching_approach' => 'Learning Arabic should be fun and practical. I use real-life scenarios and cultural context to help students speak naturally.',
                'availability' => ['Monday' => 'Evening', 'Wednesday' => 'All Day', 'Saturday' => 'Morning'],
                'rating' => 4.7,
                'total_students' => 195,
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Dr. Aisha Al-Andalusi',
                'title' => 'Arabic Grammar & Syntax Expert',
                'bio' => 'Dr. Aisha makes Arabic grammar accessible and logical. Her systematic approach helps students understand the beauty and structure of the Arabic language.',
                'email' => '<EMAIL>',
                'specializations' => ['Arabic Grammar', 'Arabic Syntax', 'Classical Arabic'],
                'languages' => ['Arabic', 'English', 'Turkish'],
                'experience_years' => 14,
                'education' => 'PhD in Arabic Linguistics, University of Damascus',
                'certifications' => ['Arabic Linguistics PhD', 'Classical Arabic Certificate'],
                'teaching_approach' => 'Grammar is the key to understanding Arabic. I break down complex rules into simple, memorable patterns.',
                'availability' => ['Sunday' => 'Morning', 'Tuesday' => 'All Day', 'Thursday' => 'Morning'],
                'rating' => 4.9,
                'total_students' => 340,
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Ustadh Khalid Al-Shami',
                'title' => 'Beginner Arabic Specialist',
                'bio' => 'Ustadh Khalid has a special talent for teaching complete beginners. His patient and encouraging approach helps nervous students gain confidence quickly.',
                'email' => '<EMAIL>',
                'specializations' => ['Beginner Arabic', 'Arabic Alphabet', 'Basic Conversation'],
                'languages' => ['Arabic', 'English', 'Italian'],
                'experience_years' => 8,
                'education' => 'Bachelor in Arabic Education, Jordan University',
                'certifications' => ['Arabic Teaching Certificate', 'TESOL Certificate'],
                'teaching_approach' => 'Every expert was once a beginner. I create a supportive environment where students feel comfortable making mistakes and learning.',
                'availability' => ['Monday' => 'Morning', 'Wednesday' => 'Evening', 'Friday' => 'All Day'],
                'rating' => 4.8,
                'total_students' => 220,
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 6,
            ],
        ];

        foreach ($teachers as $teacherData) {
            Teacher::create($teacherData);
        }
    }
}
