<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Checkout - {{ $book->title }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen py-16">
        <div class="max-w-4xl mx-auto px-4">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">Complete Your Purchase</h1>
                <p class="text-xl text-gray-600">Secure checkout for your digital book</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Order Summary -->
                <div class="bg-white rounded-2xl shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Order Summary</h2>
                    
                    <div class="flex items-start space-x-4 mb-6">
                        @if($book->image)
                            <img src="{{ Storage::url($book->image) }}" alt="{{ $book->title }}" class="w-20 h-28 object-cover rounded-lg">
                        @else
                            <div class="w-20 h-28 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg flex items-center justify-center">
                                <i class="fas fa-book text-white text-2xl"></i>
                            </div>
                        @endif
                        
                        <div class="flex-1">
                            <h3 class="text-lg font-bold text-gray-900 mb-2">{{ $book->title }}</h3>
                            <p class="text-gray-600 mb-2">by {{ $book->author }}</p>
                            @if($book->pages)
                                <p class="text-sm text-gray-500">{{ $book->pages }} pages</p>
                            @endif
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-6">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-gray-600">Book Price:</span>
                            <span class="text-lg font-semibold text-gray-900">${{ number_format($book->price, 2) }}</span>
                        </div>
                        <div class="flex justify-between items-center text-xl font-bold text-gray-900 border-t border-gray-200 pt-4">
                            <span>Total:</span>
                            <span>${{ number_format($book->price, 2) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Form -->
                <div class="bg-white rounded-2xl shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Payment Information</h2>
                    
                    <form id="payment-form" class="space-y-6">
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        
                        <!-- Customer Information -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-900">Customer Information</h3>
                            
                            <div>
                                <label for="customer_name" class="block text-sm font-semibold text-gray-700 mb-2">Full Name</label>
                                <input type="text" id="customer_name" name="customer_name" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            
                            <div>
                                <label for="customer_email" class="block text-sm font-semibold text-gray-700 mb-2">Email Address</label>
                                <input type="email" id="customer_email" name="customer_email" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <p class="text-sm text-gray-500 mt-1">Download link will be sent to this email</p>
                            </div>
                        </div>

                        <!-- Payment Methods -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-900">Payment Method</h3>
                            
                            @foreach($paymentMethods as $method => $details)
                                <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 transition-colors">
                                    <input type="radio" name="payment_method" value="{{ $method }}" class="mr-4" required>
                                    <i class="{{ $details['icon'] }} text-xl mr-4 text-gray-600"></i>
                                    <div>
                                        <div class="font-semibold text-gray-900">{{ $details['name'] }}</div>
                                        <div class="text-sm text-gray-500">{{ $details['description'] }}</div>
                                    </div>
                                </label>
                            @endforeach
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" id="submit-payment" 
                            class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold py-4 px-6 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                            <span id="button-text">
                                <i class="fas fa-lock mr-2"></i>
                                Complete Purchase - ${{ number_format($book->price, 2) }}
                            </span>
                            <span id="button-loading" class="hidden">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Processing...
                            </span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('payment-form');
            const submitButton = document.getElementById('submit-payment');
            const buttonText = document.getElementById('button-text');
            const buttonLoading = document.getElementById('button-loading');
            
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                console.log('Form submitted');
                
                const formData = new FormData(form);
                const paymentMethod = formData.get('payment_method');
                
                console.log('Payment method:', paymentMethod);
                
                if (!paymentMethod) {
                    alert('Please select a payment method');
                    return;
                }
                
                // Show loading state
                submitButton.disabled = true;
                buttonText.classList.add('hidden');
                buttonLoading.classList.remove('hidden');
                
                try {
                    // Add order ID for PayPal
                    if (paymentMethod === 'paypal') {
                        formData.append('order_id', 'PAYPAL_' + Date.now());
                    }
                    
                    console.log('Sending request to:', '{{ route("books.payment", $book) }}');
                    
                    const response = await fetch('{{ route("books.payment", $book) }}', {
                        method: 'POST',
                        body: formData
                    });
                    
                    console.log('Response status:', response.status);
                    
                    const result = await response.json();
                    console.log('Response data:', result);
                    
                    if (result.success) {
                        alert('Payment successful! Redirecting to download page...');
                        window.location.href = result.download_url;
                    } else {
                        alert(result.error || 'Payment failed');
                    }
                    
                } catch (error) {
                    console.error('Payment error:', error);
                    alert('Payment failed: ' + error.message);
                } finally {
                    // Reset button state
                    submitButton.disabled = false;
                    buttonText.classList.remove('hidden');
                    buttonLoading.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>
