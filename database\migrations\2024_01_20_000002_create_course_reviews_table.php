<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('course_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('course_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('email');
            $table->integer('rating')->unsigned()->default(5);
            $table->text('review');
            $table->boolean('is_approved')->default(false);
            $table->string('ip_address')->nullable();
            $table->timestamps();

            $table->index(['course_id', 'is_approved']);
            $table->index('rating');
        });
    }

    public function down()
    {
        Schema::dropIfExists('course_reviews');
    }
};
