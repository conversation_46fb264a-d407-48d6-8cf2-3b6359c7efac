<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('teachers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('title'); // e.g., "Arabic Language Instructor"
            $table->text('bio');
            $table->string('image')->nullable();
            $table->string('email')->nullable();
            $table->json('specializations'); // ["Arabic Language", "Quran Recitation"]
            $table->json('languages'); // ["Arabic", "English", "French"]
            $table->integer('experience_years');
            $table->string('education')->nullable();
            $table->json('certifications')->nullable(); // ["TESOL", "Ijazah in Quran"]
            $table->text('teaching_approach')->nullable();
            $table->json('availability')->nullable(); // {"days": ["Monday", "Tuesday"], "times": ["Morning", "Evening"]}
            $table->decimal('rating', 3, 2)->default(5.00); // 0.00 to 5.00
            $table->integer('total_students')->default(0);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('teachers');
    }
};
