<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            SettingsSeeder::class,
            CategoriesSeeder::class,
            CoursesSeeder::class,
            PostsSeeder::class,
            TestimonialsSeeder::class,
            BooksSeeder::class,
        ]);

        // Create admin user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password123'), // Default password
                'email_verified_at' => now(),
            ]
        );
    }
}
