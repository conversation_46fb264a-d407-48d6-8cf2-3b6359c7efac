<x-layouts.app>
    <x-slot name="title">Purchase Successful - {{ $purchase->book->title }} | Zajel Arabic Academy</x-slot>
    <x-slot name="metaDescription">Your purchase was successful! Download your book now.</x-slot>

    <!-- Success Section -->
    <section class="py-16 bg-gray-50 min-h-screen">
        <div class="max-w-4xl mx-auto container-padding">
            <!-- Success Message -->
            <div class="text-center mb-12">
                <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-check text-white text-3xl"></i>
                </div>
                <h1 class="text-4xl font-bold text-gray-900 mb-4">Payment Successful!</h1>
                <p class="text-xl text-gray-600">Thank you for your purchase. Your book is ready for download.</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Purchase Details -->
                <div class="bg-white rounded-2xl shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Purchase Details</h2>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Order ID:</span>
                            <span class="font-semibold text-gray-900">#{{ $purchase->id }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Customer:</span>
                            <span class="font-semibold text-gray-900">{{ $purchase->customer_name }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Email:</span>
                            <span class="font-semibold text-gray-900">{{ $purchase->customer_email }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Amount Paid:</span>
                            <span class="font-semibold text-gray-900">${{ number_format($purchase->amount, 2) }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Payment Method:</span>
                            <span class="font-semibold text-gray-900 capitalize">{{ $purchase->payment_provider }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Purchase Date:</span>
                            <span class="font-semibold text-gray-900">{{ $purchase->created_at->format('M d, Y') }}</span>
                        </div>
                    </div>

                    <!-- Download Info -->
                    <div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <h3 class="text-lg font-semibold text-blue-900 mb-2">Download Information</h3>
                        <div class="space-y-2 text-sm text-blue-800">
                            <p><strong>Downloads Used:</strong> {{ $purchase->download_count }} of {{ $purchase->max_downloads }}</p>
                            <p><strong>Download Link Expires:</strong> {{ $purchase->expires_at->format('M d, Y') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Book Details & Download -->
                <div class="bg-white rounded-2xl shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Your Book</h2>
                    
                    <div class="flex items-start space-x-4 mb-6">
                        @if($purchase->book->image)
                            <img src="{{ Storage::url($purchase->book->image) }}" alt="{{ $purchase->book->title }}" class="w-24 h-32 object-cover rounded-lg">
                        @else
                            <div class="w-24 h-32 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg flex items-center justify-center">
                                <i class="fas fa-book text-white text-3xl"></i>
                            </div>
                        @endif
                        
                        <div class="flex-1">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $purchase->book->title }}</h3>
                            <p class="text-gray-600 mb-2">by {{ $purchase->book->author }}</p>
                            @if($purchase->book->pages)
                                <p class="text-sm text-gray-500 mb-2">{{ $purchase->book->pages }} pages</p>
                            @endif
                            @if($purchase->book->file_size)
                                <p class="text-sm text-gray-500">File Size: {{ $purchase->book->file_size }}</p>
                            @endif
                        </div>
                    </div>

                    <!-- Download Button -->
                    @if($purchase->canDownload())
                        <a href="{{ route('books.download.purchased', $purchase->download_token) }}" 
                           class="block w-full bg-gradient-to-r from-green-600 to-green-700 text-white text-center font-bold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl hover:from-green-700 hover:to-green-800 transform hover:scale-105 transition-all duration-200 mb-4">
                            <i class="fas fa-download mr-3"></i>
                            Download Your Book
                        </a>
                        
                        <p class="text-sm text-gray-500 text-center">
                            You have {{ $purchase->max_downloads - $purchase->download_count }} downloads remaining
                        </p>
                    @else
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                            <i class="fas fa-exclamation-triangle text-red-600 text-2xl mb-2"></i>
                            <p class="text-red-800 font-semibold">Download Not Available</p>
                            <p class="text-red-600 text-sm">
                                @if($purchase->download_count >= $purchase->max_downloads)
                                    You have exceeded the maximum number of downloads.
                                @elseif($purchase->expires_at < now())
                                    Your download link has expired.
                                @else
                                    Download is currently unavailable.
                                @endif
                            </p>
                        </div>
                    @endif

                    <!-- Support Info -->
                    <div class="mt-8 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                        <h4 class="font-semibold text-gray-900 mb-2">Need Help?</h4>
                        <p class="text-sm text-gray-600 mb-3">
                            If you have any issues with your download or need assistance, please contact our support team.
                        </p>
                        <a href="/contact" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold text-sm">
                            <i class="fas fa-envelope mr-2"></i>
                            Contact Support
                        </a>
                    </div>
                </div>
            </div>

            <!-- Additional Actions -->
            <div class="mt-12 text-center space-y-4">
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="/books" class="btn bg-gray-600 text-white hover:bg-gray-700 font-semibold px-8 py-3">
                        <i class="fas fa-book mr-2"></i>
                        Browse More Books
                    </a>
                    
                    <a href="/courses" class="btn bg-blue-600 text-white hover:bg-blue-700 font-semibold px-8 py-3">
                        <i class="fas fa-graduation-cap mr-2"></i>
                        Explore Courses
                    </a>
                </div>

                <!-- Email Confirmation -->
                <div class="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg max-w-2xl mx-auto">
                    <div class="flex items-center justify-center">
                        <i class="fas fa-envelope text-yellow-600 text-xl mr-3"></i>
                        <div class="text-left">
                            <p class="text-yellow-800 font-semibold">Confirmation Email Sent</p>
                            <p class="text-yellow-700 text-sm">
                                A confirmation email with your download link has been sent to {{ $purchase->customer_email }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</x-layouts.app>
