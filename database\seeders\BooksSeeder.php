<?php

namespace Database\Seeders;

use App\Models\Book;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class BooksSeeder extends Seeder
{
    public function run(): void
    {
        $books = [
            [
                'title' => 'Arabic Alphabet Workbook for Beginners',
                'slug' => 'arabic-alphabet-workbook-beginners',
                'author' => 'Dr. <PERSON>',
                'description' => 'A comprehensive workbook designed to help beginners master the Arabic alphabet. Features step-by-step writing guides, pronunciation tips, and practice exercises for each letter. Perfect for self-study or classroom use.',
                'is_free' => true,
                'price' => 0,
                'pages' => 45,
                'language' => 'English/Arabic',
                'file_size' => '2.5 MB',
                'isbn' => '978-1234567890',
                'publication_date' => now()->subMonths(6),
                'is_featured' => true,
                'is_active' => true,
                'download_count' => 1250,
                'sort_order' => 1,
                'meta_title' => 'Free Arabic Alphabet Workbook for Beginners - Download PDF',
                'meta_description' => 'Download our free Arabic alphabet workbook with step-by-step writing guides and practice exercises. Perfect for beginners learning Arabic.',
            ],
            [
                'title' => 'Essential Arabic Vocabulary - 1000 Most Common Words',
                'slug' => 'essential-arabic-vocabulary-1000-words',
                'author' => 'Ustadha Fatima Al-Arabiya',
                'description' => 'Master the 1000 most frequently used Arabic words with this comprehensive vocabulary guide. Each word includes pronunciation, meaning, example sentences, and usage notes. Organized by topics for easy learning.',
                'is_free' => true,
                'price' => 0,
                'pages' => 120,
                'language' => 'English/Arabic',
                'file_size' => '4.2 MB',
                'isbn' => '978-1234567891',
                'publication_date' => now()->subMonths(4),
                'is_featured' => true,
                'is_active' => true,
                'download_count' => 890,
                'sort_order' => 2,
                'meta_title' => 'Essential Arabic Vocabulary - 1000 Most Common Words PDF',
                'meta_description' => 'Learn the 1000 most common Arabic words with pronunciation, meanings, and examples. Free PDF download.',
            ],
            [
                'title' => 'Tajweed Rules Made Simple',
                'slug' => 'tajweed-rules-made-simple',
                'author' => 'Sheikh Omar Al-Qari',
                'description' => 'A beginner-friendly guide to Tajweed rules with clear explanations, practical examples, and audio references. Learn proper Quran recitation with this step-by-step manual covering all essential Tajweed principles.',
                'is_free' => false,
                'price' => 15,
                'pages' => 85,
                'language' => 'English/Arabic',
                'file_size' => '3.8 MB',
                'isbn' => '978-1234567892',
                'publication_date' => now()->subMonths(3),
                'is_featured' => true,
                'is_active' => true,
                'download_count' => 456,
                'sort_order' => 3,
                'meta_title' => 'Tajweed Rules Made Simple - Complete Guide PDF',
                'meta_description' => 'Master Tajweed rules with our comprehensive guide. Clear explanations and practical examples for perfect Quran recitation.',
            ],
            [
                'title' => 'Arabic Grammar Fundamentals',
                'slug' => 'arabic-grammar-fundamentals',
                'author' => 'Dr. Muhammad Al-Nahwi',
                'description' => 'Comprehensive guide to Arabic grammar covering nouns, verbs, sentence structure, and essential grammatical concepts. Includes exercises and examples to reinforce learning.',
                'is_free' => true,
                'price' => 0,
                'pages' => 95,
                'language' => 'English/Arabic',
                'file_size' => '3.1 MB',
                'isbn' => '978-1234567893',
                'publication_date' => now()->subMonths(8),
                'is_featured' => false,
                'is_active' => true,
                'download_count' => 723,
                'sort_order' => 4,
                'meta_title' => 'Arabic Grammar Fundamentals - Free PDF Guide',
                'meta_description' => 'Learn Arabic grammar fundamentals with our comprehensive guide covering nouns, verbs, and sentence structure.',
            ],
            [
                'title' => 'Islamic Stories for Children (Arabic-English)',
                'slug' => 'islamic-stories-children-arabic-english',
                'author' => 'Sister Aisha Al-Qasas',
                'description' => 'Beautiful collection of Islamic stories for children presented in both Arabic and English. Perfect for teaching Islamic values while improving Arabic reading skills.',
                'is_free' => true,
                'price' => 0,
                'pages' => 60,
                'language' => 'Arabic/English',
                'file_size' => '5.2 MB',
                'isbn' => '978-1234567894',
                'publication_date' => now()->subMonths(2),
                'is_featured' => false,
                'is_active' => true,
                'download_count' => 634,
                'sort_order' => 5,
                'meta_title' => 'Islamic Stories for Children - Arabic English PDF',
                'meta_description' => 'Beautiful Islamic stories for children in Arabic and English. Perfect for teaching values and language skills.',
            ],
            [
                'title' => 'Quran Memorization Techniques and Strategies',
                'slug' => 'quran-memorization-techniques-strategies',
                'author' => 'Ustadh Yusuf Al-Hafiz',
                'description' => 'Proven methods and techniques for effective Quran memorization. Includes scheduling templates, review strategies, and tips from experienced huffaz.',
                'is_free' => false,
                'price' => 12,
                'pages' => 75,
                'language' => 'English',
                'file_size' => '2.8 MB',
                'isbn' => '978-1234567895',
                'publication_date' => now()->subMonths(5),
                'is_featured' => false,
                'is_active' => true,
                'download_count' => 312,
                'sort_order' => 6,
                'meta_title' => 'Quran Memorization Techniques - Complete Guide PDF',
                'meta_description' => 'Master Quran memorization with proven techniques and strategies from experienced instructors.',
            ],
            [
                'title' => 'Arabic Conversation Phrases for Daily Life',
                'slug' => 'arabic-conversation-phrases-daily-life',
                'author' => 'Dr. Layla Al-Muhadatha',
                'description' => 'Essential Arabic phrases and expressions for everyday conversations. Covers greetings, shopping, dining, travel, and common social situations.',
                'is_free' => true,
                'price' => 0,
                'pages' => 55,
                'language' => 'English/Arabic',
                'file_size' => '2.1 MB',
                'isbn' => '978-1234567896',
                'publication_date' => now()->subMonths(7),
                'is_featured' => false,
                'is_active' => true,
                'download_count' => 567,
                'sort_order' => 7,
                'meta_title' => 'Arabic Conversation Phrases for Daily Life - Free PDF',
                'meta_description' => 'Learn essential Arabic phrases for daily conversations. Free guide covering greetings, shopping, dining, and more.',
            ],
            [
                'title' => 'Introduction to Islamic Jurisprudence (Fiqh)',
                'slug' => 'introduction-islamic-jurisprudence-fiqh',
                'author' => 'Sheikh Abdullah Al-Faqih',
                'description' => 'Beginner-friendly introduction to Islamic jurisprudence covering the basics of worship, transactions, and Islamic law principles.',
                'is_free' => false,
                'price' => 18,
                'pages' => 110,
                'language' => 'English',
                'file_size' => '4.5 MB',
                'isbn' => '978-1234567897',
                'publication_date' => now()->subMonths(9),
                'is_featured' => false,
                'is_active' => true,
                'download_count' => 234,
                'sort_order' => 8,
                'meta_title' => 'Introduction to Islamic Jurisprudence (Fiqh) - PDF Guide',
                'meta_description' => 'Learn the basics of Islamic jurisprudence with this comprehensive introduction to Fiqh principles.',
            ],
        ];

        foreach ($books as $book) {
            Book::updateOrCreate(
                ['slug' => $book['slug']],
                $book
            );
        }
    }
}
