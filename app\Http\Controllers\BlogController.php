<?php

namespace App\Http\Controllers;

use App\Models\Post;
use Illuminate\Http\Request;

class BlogController extends Controller
{
    public function index(Request $request)
    {
        $query = Post::published()->with('author');

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%")
                  ->orWhere('author_name', 'like', "%{$search}%");
            });
        }

        // Filter by tag
        if ($request->has('tag') && $request->tag) {
            $query->whereJsonContains('tags', $request->tag);
        }

        // Sort
        $sortBy = $request->get('sort', 'latest');
        switch ($sortBy) {
            case 'popular':
                $query->orderBy('views_count', 'desc');
                break;
            case 'oldest':
                $query->orderBy('published_at', 'asc');
                break;
            case 'latest':
            default:
                $query->orderBy('published_at', 'desc');
                break;
        }

        $posts = $query->paginate(9);

        // Get featured posts
        $featuredPosts = Post::published()
            ->featured()
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        // Get popular tags
        $popularTags = Post::published()
            ->whereNotNull('tags')
            ->get()
            ->pluck('tags')
            ->flatten()
            ->countBy()
            ->sortDesc()
            ->take(10)
            ->keys();

        return view('blog.index', compact('posts', 'featuredPosts', 'popularTags'));
    }

    public function show(Post $post)
    {
        // Check if post is published
        if (!$post->is_published || $post->published_at > now()) {
            abort(404);
        }

        // Increment views count
        $post->incrementViews();

        // Get related posts
        $relatedPosts = Post::published()
            ->where('id', '!=', $post->id)
            ->when($post->tags, function($query) use ($post) {
                $query->where(function($q) use ($post) {
                    foreach ($post->tags as $tag) {
                        $q->orWhereJsonContains('tags', $tag);
                    }
                });
            })
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        // If no related posts by tags, get latest posts
        if ($relatedPosts->count() < 3) {
            $relatedPosts = Post::published()
                ->where('id', '!=', $post->id)
                ->orderBy('published_at', 'desc')
                ->take(3)
                ->get();
        }

        return view('blog.show', compact('post', 'relatedPosts'));
    }
}
