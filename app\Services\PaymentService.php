<?php

namespace App\Services;

use App\Models\Book;
use App\Models\BookPurchase;
use App\Models\PaymentSetting;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PaymentService
{
    public function createPurchase(Book $book, array $customerData): BookPurchase
    {
        return BookPurchase::create([
            'book_id' => $book->id,
            'customer_email' => $customerData['email'],
            'customer_name' => $customerData['name'],
            'amount' => $book->price,
            'currency' => 'USD',
            'payment_status' => 'pending',
        ]);
    }

    public function getAvailablePaymentMethods(): array
    {
        $methods = [];
        $providers = PaymentSetting::getEnabledProviders();

        if ($providers->has('stripe')) {
            $methods['stripe'] = [
                'name' => 'Credit Card (Stripe)',
                'icon' => 'fas fa-credit-card',
                'description' => 'Pay securely with your credit or debit card'
            ];
        }

        if ($providers->has('paypal')) {
            $methods['paypal'] = [
                'name' => 'PayPal',
                'icon' => 'fab fa-paypal',
                'description' => 'Pay with your PayPal account'
            ];
        }

        return $methods;
    }

    public function processStripePayment(BookPurchase $purchase, array $paymentData): array
    {
        try {
            $stripeSettings = PaymentSetting::where('provider', 'stripe')->first();

            if (!$stripeSettings || !$stripeSettings->is_enabled) {
                throw new \Exception('Stripe payment is not available');
            }

            \Stripe\Stripe::setApiKey($stripeSettings->getDecryptedSecretKey());

            $paymentIntent = \Stripe\PaymentIntent::create([
                'amount' => $purchase->amount * 100, // Convert to cents
                'currency' => strtolower($purchase->currency),
                'payment_method' => $paymentData['payment_method_id'],
                'confirmation_method' => 'manual',
                'confirm' => true,
                'return_url' => route('books.purchase.success', $purchase->download_token),
                'metadata' => [
                    'purchase_id' => $purchase->id,
                    'book_id' => $purchase->book_id,
                    'customer_email' => $purchase->customer_email,
                ],
            ]);

            $purchase->update([
                'payment_id' => $paymentIntent->id,
                'payment_provider' => 'stripe',
                'payment_data' => [
                    'payment_intent_id' => $paymentIntent->id,
                    'status' => $paymentIntent->status,
                ],
            ]);

            if ($paymentIntent->status === 'succeeded') {
                $purchase->update(['payment_status' => 'completed']);
                return ['success' => true, 'purchase' => $purchase];
            }

            return [
                'success' => false,
                'requires_action' => $paymentIntent->status === 'requires_action',
                'payment_intent' => $paymentIntent,
            ];

        } catch (\Exception $e) {
            Log::error('Stripe payment failed', [
                'purchase_id' => $purchase->id,
                'error' => $e->getMessage(),
            ]);

            $purchase->update(['payment_status' => 'failed']);

            return [
                'success' => false,
                'error' => 'Payment processing failed. Please try again.',
            ];
        }
    }

    public function processPayPalPayment(BookPurchase $purchase, array $paymentData): array
    {
        try {
            $paypalSettings = PaymentSetting::where('provider', 'paypal')->first();

            if (!$paypalSettings || !$paypalSettings->is_enabled) {
                throw new \Exception('PayPal payment is not available');
            }

            // PayPal integration would go here
            // For now, we'll simulate a successful payment

            $purchase->update([
                'payment_id' => $paymentData['order_id'] ?? Str::random(20),
                'payment_provider' => 'paypal',
                'payment_status' => 'completed',
                'payment_data' => $paymentData,
            ]);

            return ['success' => true, 'purchase' => $purchase];

        } catch (\Exception $e) {
            Log::error('PayPal payment failed', [
                'purchase_id' => $purchase->id,
                'error' => $e->getMessage(),
            ]);

            $purchase->update(['payment_status' => 'failed']);

            return [
                'success' => false,
                'error' => 'Payment processing failed. Please try again.',
            ];
        }
    }

    public function handleWebhook(string $provider, array $payload): bool
    {
        try {
            switch ($provider) {
                case 'stripe':
                    return $this->handleStripeWebhook($payload);
                case 'paypal':
                    return $this->handlePayPalWebhook($payload);
                default:
                    return false;
            }
        } catch (\Exception $e) {
            Log::error("Webhook handling failed for {$provider}", [
                'error' => $e->getMessage(),
                'payload' => $payload,
            ]);
            return false;
        }
    }

    private function handleStripeWebhook(array $payload): bool
    {
        $event = $payload['type'] ?? null;

        if ($event === 'payment_intent.succeeded') {
            $paymentIntent = $payload['data']['object'];
            $purchaseId = $paymentIntent['metadata']['purchase_id'] ?? null;

            if ($purchaseId) {
                $purchase = BookPurchase::find($purchaseId);
                if ($purchase && $purchase->payment_status !== 'completed') {
                    $purchase->update(['payment_status' => 'completed']);
                    return true;
                }
            }
        }

        return false;
    }

    private function handlePayPalWebhook(array $payload): bool
    {
        // PayPal webhook handling would go here
        return true;
    }

    public function testConnection(PaymentSetting $setting): array
    {
        try {
            switch ($setting->provider) {
                case 'stripe':
                    return $this->testStripeConnection($setting);
                case 'paypal':
                    return $this->testPayPalConnection($setting);
                default:
                    return ['success' => false, 'message' => 'Unknown provider'];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            ];
        }
    }

    private function testStripeConnection(PaymentSetting $setting): array
    {
        try {
            \Stripe\Stripe::setApiKey($setting->getDecryptedSecretKey());

            // Test by retrieving account info
            $account = \Stripe\Account::retrieve();

            return [
                'success' => true,
                'message' => "Connected to Stripe account: {$account->display_name}",
                'details' => [
                    'account_id' => $account->id,
                    'country' => $account->country,
                    'currency' => $account->default_currency,
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Stripe connection failed: ' . $e->getMessage()
            ];
        }
    }

    private function testPayPalConnection(PaymentSetting $setting): array
    {
        try {
            // PayPal connection test would go here
            // For now, just validate that keys are present
            if (empty($setting->getDecryptedPublicKey()) || empty($setting->getDecryptedSecretKey())) {
                throw new \Exception('Client ID and Secret are required');
            }

            return [
                'success' => true,
                'message' => 'PayPal credentials configured (full test coming soon)',
                'details' => [
                    'client_id' => substr($setting->getDecryptedPublicKey(), 0, 10) . '...',
                    'sandbox' => $setting->is_sandbox,
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'PayPal connection failed: ' . $e->getMessage()
            ];
        }
    }
}
