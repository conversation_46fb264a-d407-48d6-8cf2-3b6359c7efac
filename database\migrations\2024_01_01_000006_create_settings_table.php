<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->longText('value')->nullable();
            $table->string('type')->default('text'); // text, textarea, boolean, integer, json, image
            $table->string('group')->default('general'); // general, home, contact, seo, etc.
            $table->string('description')->nullable();
            $table->timestamps();
            
            $table->index(['group']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
