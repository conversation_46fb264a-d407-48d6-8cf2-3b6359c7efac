<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    public function run(): void
    {
        $settings = [
            // General Settings
            ['key' => 'site_name', 'value' => 'Zajel Arabic Academy', 'type' => 'text', 'group' => 'general'],
            ['key' => 'site_description', 'value' => 'Learn Arabic, Quran Memorization, and Quran Recitation with Expert Instructors', 'type' => 'textarea', 'group' => 'general'],
            ['key' => 'site_logo', 'value' => '', 'type' => 'image', 'group' => 'general'],
            ['key' => 'site_favicon', 'value' => '', 'type' => 'image', 'group' => 'general'],
            ['key' => 'contact_email', 'value' => '<EMAIL>', 'type' => 'text', 'group' => 'contact'],
            ['key' => 'contact_phone', 'value' => '+1234567890', 'type' => 'text', 'group' => 'contact'],
            ['key' => 'contact_address', 'value' => '', 'type' => 'textarea', 'group' => 'contact'],
            
            // Home Page Settings
            ['key' => 'hero_title', 'value' => 'Learn Quran & Arabic Language', 'type' => 'text', 'group' => 'home'],
            ['key' => 'hero_subtitle', 'value' => 'Master Arabic language and Quran recitation with our expert instructors through interactive online courses designed for non-Arabic speakers.', 'type' => 'textarea', 'group' => 'home'],
            ['key' => 'hero_image', 'value' => '', 'type' => 'image', 'group' => 'home'],
            ['key' => 'hero_video_url', 'value' => '', 'type' => 'text', 'group' => 'home'],
            
            // About Section
            ['key' => 'about_title', 'value' => 'About Zajel Arabic Academy', 'type' => 'text', 'group' => 'about'],
            ['key' => 'about_description', 'value' => 'We are dedicated to teaching Arabic language and Quran to non-Arabic speakers worldwide through innovative online learning methods.', 'type' => 'textarea', 'group' => 'about'],
            ['key' => 'about_image', 'value' => '', 'type' => 'image', 'group' => 'about'],
            
            // Mission & Vision
            ['key' => 'our_mission', 'value' => 'To make Arabic language and Quran learning accessible to everyone around the world through high-quality online education.', 'type' => 'textarea', 'group' => 'about'],
            ['key' => 'our_vision', 'value' => 'To become the leading platform for Arabic and Quran education, connecting students globally with expert instructors.', 'type' => 'textarea', 'group' => 'about'],
            
            // Social Media
            ['key' => 'facebook_url', 'value' => '', 'type' => 'text', 'group' => 'social'],
            ['key' => 'twitter_url', 'value' => '', 'type' => 'text', 'group' => 'social'],
            ['key' => 'instagram_url', 'value' => '', 'type' => 'text', 'group' => 'social'],
            ['key' => 'youtube_url', 'value' => '', 'type' => 'text', 'group' => 'social'],
            ['key' => 'linkedin_url', 'value' => '', 'type' => 'text', 'group' => 'social'],
            
            // SEO Settings
            ['key' => 'meta_title', 'value' => 'Zajel Arabic Academy - Learn Arabic & Quran Online', 'type' => 'text', 'group' => 'seo'],
            ['key' => 'meta_description', 'value' => 'Learn Arabic language, Quran memorization, and recitation with expert instructors. Online courses for non-Arabic speakers worldwide.', 'type' => 'textarea', 'group' => 'seo'],
            ['key' => 'meta_keywords', 'value' => 'Arabic learning, Quran memorization, Quran recitation, online Arabic courses, Islamic education', 'type' => 'textarea', 'group' => 'seo'],
            
            // Features
            ['key' => 'features_enabled', 'value' => true, 'type' => 'boolean', 'group' => 'features'],
            ['key' => 'testimonials_enabled', 'value' => true, 'type' => 'boolean', 'group' => 'features'],
            ['key' => 'blog_enabled', 'value' => true, 'type' => 'boolean', 'group' => 'features'],
            ['key' => 'books_enabled', 'value' => true, 'type' => 'boolean', 'group' => 'features'],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
