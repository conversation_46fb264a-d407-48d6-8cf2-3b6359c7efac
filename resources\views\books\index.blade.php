<x-layouts.app>
    <x-slot name="title">Free Arabic & Islamic Books - Zajel Arabic Academy</x-slot>
    <x-slot name="metaDescription">Download free Arabic language and Islamic books. Educational resources for Arabic learners and Islamic studies students worldwide.</x-slot>

    <!-- Page Header -->
    <section class="bg-gradient-primary text-white py-16 md:py-20">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                    Free Books & Resources
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed font-medium">
                    Expand your knowledge with our collection of free Arabic language and Islamic books.
                </p>
            </div>
        </div>
    </section>

    <!-- Filters Section -->
    <section class="bg-white border-b border-gray-200 sticky top-16 md:top-20 z-40">
        <div class="max-w-7xl mx-auto container-padding py-6">
            <form method="GET" class="flex flex-wrap items-center gap-4">
                <!-- Search -->
                <div class="flex-1 min-w-64">
                    <div class="relative">
                        <input 
                            type="text" 
                            name="search" 
                            value="{{ request('search') }}"
                            placeholder="Search books..." 
                            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium"
                        >
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <!-- Type Filter -->
                <select name="type" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium">
                    <option value="">All Books</option>
                    <option value="free" {{ request('type') === 'free' ? 'selected' : '' }}>Free Books</option>
                    <option value="paid" {{ request('type') === 'paid' ? 'selected' : '' }}>Premium Books</option>
                </select>

                <!-- Language Filter -->
                @if($languages->count() > 0)
                <select name="language" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium">
                    <option value="">All Languages</option>
                    @foreach($languages as $language)
                        <option value="{{ $language }}" {{ request('language') === $language ? 'selected' : '' }}>
                            {{ $language }}
                        </option>
                    @endforeach
                </select>
                @endif

                <!-- Sort -->
                <select name="sort" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium">
                    <option value="featured" {{ request('sort') === 'featured' ? 'selected' : '' }}>Featured</option>
                    <option value="title" {{ request('sort') === 'title' ? 'selected' : '' }}>Title A-Z</option>
                    <option value="author" {{ request('sort') === 'author' ? 'selected' : '' }}>Author A-Z</option>
                    <option value="newest" {{ request('sort') === 'newest' ? 'selected' : '' }}>Newest</option>
                    <option value="popular" {{ request('sort') === 'popular' ? 'selected' : '' }}>Most Downloaded</option>
                </select>

                <!-- Filter Button -->
                <button type="submit" class="btn btn-primary font-semibold px-6 py-3">
                    <i class="fas fa-filter mr-2"></i>
                    Filter
                </button>

                <!-- Clear Filters -->
                @if(request()->hasAny(['search', 'type', 'language', 'sort']))
                    <a href="/books" class="btn btn-outline font-semibold px-6 py-3">
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </a>
                @endif
            </form>
        </div>
    </section>

    <div class="max-w-7xl mx-auto container-padding section-padding">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-3">
                <!-- Featured Books -->
                @if($featuredBooks->count() > 0 && !request()->hasAny(['search', 'type', 'language', 'sort']))
                <section class="mb-16">
                    <h2 class="text-3xl font-bold text-gray-900 mb-8">Featured Books</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        @foreach($featuredBooks as $book)
                            <div class="card card-hover group">
                                <!-- Book Cover -->
                                <div class="relative overflow-hidden">
                                    @if($book->image)
                                        <img 
                                            src="{{ Storage::url($book->image) }}" 
                                            alt="{{ $book->title }}"
                                            class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                                        >
                                    @else
                                        <div class="w-full h-64 bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center">
                                            <i class="fas fa-book text-white text-4xl"></i>
                                        </div>
                                    @endif
                                    
                                    <!-- Featured Badge -->
                                    <div class="absolute top-4 left-4">
                                        <span class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                            <i class="fas fa-star mr-1"></i>
                                            Featured
                                        </span>
                                    </div>

                                    <!-- Price Badge -->
                                    <div class="absolute top-4 right-4">
                                        @if($book->is_free)
                                            <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                                Free
                                            </span>
                                        @else
                                            <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-bold">
                                                ${{ number_format($book->price, 0) }}
                                            </span>
                                        @endif
                                    </div>
                                </div>

                                <!-- Book Info -->
                                <div class="p-6">
                                    <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
                                        {{ $book->title }}
                                    </h3>
                                    <p class="text-gray-600 mb-3 font-medium">by {{ $book->author }}</p>
                                    <p class="text-gray-700 mb-4 leading-relaxed font-medium">
                                        {{ Str::limit($book->description, 100) }}
                                    </p>
                                    
                                    <!-- Book Meta -->
                                    <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                        @if($book->pages)
                                            <span class="font-medium">{{ $book->pages }} pages</span>
                                        @endif
                                        @if($book->language)
                                            <span class="font-medium">{{ $book->language }}</span>
                                        @endif
                                    </div>

                                    <a href="/books/{{ $book->slug }}" class="block w-full btn btn-primary text-center font-semibold">
                                        <i class="fas fa-eye mr-2"></i>
                                        View Details
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </section>
                @endif

                <!-- All Books -->
                <section>
                    @if(!request()->hasAny(['search', 'type', 'language', 'sort']))
                        <h2 class="text-3xl font-bold text-gray-900 mb-8">All Books</h2>
                    @endif

                    @if($books->count() > 0)
                        <!-- Results Info -->
                        <div class="flex items-center justify-between mb-8">
                            <p class="text-gray-600 font-medium">
                                Showing {{ $books->firstItem() }}-{{ $books->lastItem() }} of {{ $books->total() }} books
                            </p>
                        </div>

                        <!-- Books Grid -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                            @foreach($books as $book)
                                <div class="card card-hover group">
                                    <!-- Book Cover -->
                                    <div class="relative overflow-hidden">
                                        @if($book->image)
                                            <img 
                                                src="{{ Storage::url($book->image) }}" 
                                                alt="{{ $book->title }}"
                                                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                            >
                                        @else
                                            <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                                <i class="fas fa-book text-gray-400 text-3xl"></i>
                                            </div>
                                        @endif

                                        <!-- Price Badge -->
                                        <div class="absolute top-4 right-4">
                                            @if($book->is_free)
                                                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                                    Free
                                                </span>
                                            @else
                                                <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-bold">
                                                    ${{ number_format($book->price, 0) }}
                                                </span>
                                            @endif
                                        </div>

                                        @if($book->is_featured)
                                            <div class="absolute top-4 left-4">
                                                <span class="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold">
                                                    <i class="fas fa-star mr-1"></i>
                                                    Featured
                                                </span>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Book Info -->
                                    <div class="p-6">
                                        <h3 class="text-lg font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
                                            {{ $book->title }}
                                        </h3>
                                        <p class="text-gray-600 mb-3 font-medium">by {{ $book->author }}</p>
                                        <p class="text-gray-700 mb-4 leading-relaxed font-medium text-sm">
                                            {{ Str::limit($book->description, 80) }}
                                        </p>
                                        
                                        <!-- Book Meta -->
                                        <div class="flex items-center justify-between text-xs text-gray-500 mb-4">
                                            @if($book->pages)
                                                <span class="font-medium">{{ $book->pages }} pages</span>
                                            @endif
                                            @if($book->download_count > 0)
                                                <span class="font-medium">{{ $book->download_count }} downloads</span>
                                            @endif
                                        </div>

                                        <a href="/books/{{ $book->slug }}" class="block w-full btn btn-outline text-center font-semibold">
                                            <i class="fas fa-eye mr-2"></i>
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="flex justify-center">
                            {{ $books->appends(request()->query())->links() }}
                        </div>
                    @else
                        <!-- No Results -->
                        <div class="text-center py-16">
                            <i class="fas fa-search text-6xl text-gray-300 mb-6"></i>
                            <h3 class="text-2xl font-bold text-gray-900 mb-4">No books found</h3>
                            <p class="text-lg text-gray-600 mb-8 max-w-md mx-auto">
                                We couldn't find any books matching your criteria. Try adjusting your search terms or filters.
                            </p>
                            <a href="/books" class="btn btn-primary font-semibold">
                                <i class="fas fa-refresh mr-2"></i>
                                View All Books
                            </a>
                        </div>
                    @endif
                </section>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Quick Download -->
                <div class="card p-6 mb-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Quick Access</h3>
                    <div class="space-y-3">
                        <a href="/books?type=free" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium">
                            <i class="fas fa-download mr-3 text-green-600"></i>
                            Free Downloads
                        </a>
                        <a href="/books?sort=popular" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium">
                            <i class="fas fa-fire mr-3 text-orange-600"></i>
                            Most Popular
                        </a>
                        <a href="/books?sort=newest" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium">
                            <i class="fas fa-clock mr-3 text-blue-600"></i>
                            Recently Added
                        </a>
                        <a href="/books?language=Arabic" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium">
                            <i class="fas fa-language mr-3 text-purple-600"></i>
                            Arabic Books
                        </a>
                    </div>
                </div>

                <!-- Learning Resources -->
                <div class="card p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Learning Resources</h3>
                    <ul class="space-y-3">
                        <li>
                            <a href="/courses" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium">
                                <i class="fas fa-graduation-cap mr-3 text-primary-600"></i>
                                Online Courses
                            </a>
                        </li>
                        <li>
                            <a href="/blog" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium">
                                <i class="fas fa-blog mr-3 text-primary-600"></i>
                                Learning Articles
                            </a>
                        </li>
                        <li>
                            <a href="/trial-class" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium">
                                <i class="fas fa-play-circle mr-3 text-primary-600"></i>
                                Free Trial Class
                            </a>
                        </li>
                        <li>
                            <a href="/contact" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium">
                                <i class="fas fa-envelope mr-3 text-primary-600"></i>
                                Contact Support
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- CTA Section -->
    <section class="section-padding bg-gradient-primary">
        <div class="max-w-4xl mx-auto container-padding text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Want More Learning Resources?
            </h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed font-medium">
                Complement your reading with our interactive courses and personalized instruction.
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/courses" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                    <i class="fas fa-graduation-cap mr-3"></i>
                    Browse Courses
                </a>
                <a href="/trial-class" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 text-lg font-semibold px-8 py-4">
                    <i class="fas fa-play-circle mr-3"></i>
                    Try Free Class
                </a>
            </div>
        </div>
    </section>
</x-layouts.app>
