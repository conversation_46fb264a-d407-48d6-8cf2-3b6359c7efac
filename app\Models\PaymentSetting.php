<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'provider',
        'is_enabled',
        'is_sandbox',
        'public_key',
        'secret_key',
        'webhook_secret',
        'additional_settings',
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'is_sandbox' => 'boolean',
        'additional_settings' => 'array',
        'public_key' => 'encrypted',
        'secret_key' => 'encrypted',
        'webhook_secret' => 'encrypted',
    ];

    public static function getEnabledProviders()
    {
        return self::where('is_enabled', true)->get()->keyBy('provider');
    }

    public static function isProviderEnabled(string $provider): bool
    {
        return self::where('provider', $provider)
            ->where('is_enabled', true)
            ->exists();
    }

    public function getDecryptedSecretKey()
    {
        return $this->secret_key;
    }

    public function getDecryptedPublicKey()
    {
        return $this->public_key;
    }
}
