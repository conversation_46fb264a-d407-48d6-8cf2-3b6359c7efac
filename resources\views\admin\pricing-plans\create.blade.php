<x-layouts.app>
    <x-slot name="title">Add New Pricing Plan</x-slot>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="flex items-center mb-8">
                <a href="{{ route('admin.pricing-plans.index') }}" 
                   class="text-blue-600 hover:text-blue-800 mr-4">
                    ← Back to Plans
                </a>
                <h1 class="text-3xl font-bold text-gray-900">Add New Pricing Plan</h1>
            </div>

            <div class="bg-white rounded-lg shadow p-8">
                <form action="{{ route('admin.pricing-plans.store') }}" method="POST">
                    @csrf
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Plan Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Plan Name *
                            </label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name') }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="e.g., 2 Days per Week"
                                   required>
                            @error('name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Price -->
                        <div>
                            <label for="price" class="block text-sm font-medium text-gray-700 mb-2">
                                Price *
                            </label>
                            <input type="number" 
                                   id="price" 
                                   name="price" 
                                   value="{{ old('price') }}"
                                   step="0.01"
                                   min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="40.00"
                                   required>
                            @error('price')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Original Price -->
                        <div>
                            <label for="original_price" class="block text-sm font-medium text-gray-700 mb-2">
                                Original Price (for discount display)
                            </label>
                            <input type="number" 
                                   id="original_price" 
                                   name="original_price" 
                                   value="{{ old('original_price') }}"
                                   step="0.01"
                                   min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="50.00">
                            @error('original_price')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Discount Percentage -->
                        <div>
                            <label for="discount_percentage" class="block text-sm font-medium text-gray-700 mb-2">
                                Discount Percentage
                            </label>
                            <input type="number" 
                                   id="discount_percentage" 
                                   name="discount_percentage" 
                                   value="{{ old('discount_percentage', 0) }}"
                                   min="0"
                                   max="100"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="10">
                            @error('discount_percentage')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Duration -->
                        <div>
                            <label for="duration" class="block text-sm font-medium text-gray-700 mb-2">
                                Duration *
                            </label>
                            <input type="text" 
                                   id="duration" 
                                   name="duration" 
                                   value="{{ old('duration', 'per month') }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="per month"
                                   required>
                            @error('duration')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Button Text -->
                        <div>
                            <label for="button_text" class="block text-sm font-medium text-gray-700 mb-2">
                                Button Text *
                            </label>
                            <input type="text" 
                                   id="button_text" 
                                   name="button_text" 
                                   value="{{ old('button_text', 'Start Free Trial') }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="Start Free Trial"
                                   required>
                            @error('button_text')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Button URL -->
                        <div>
                            <label for="button_url" class="block text-sm font-medium text-gray-700 mb-2">
                                Button URL
                            </label>
                            <input type="url" 
                                   id="button_url" 
                                   name="button_url" 
                                   value="{{ old('button_url') }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="https://example.com/signup">
                            @error('button_url')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Sort Order -->
                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                                Sort Order
                            </label>
                            <input type="number" 
                                   id="sort_order" 
                                   name="sort_order" 
                                   value="{{ old('sort_order', 0) }}"
                                   min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="0">
                            @error('sort_order')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Features *
                        </label>
                        <div id="features-container">
                            @if(old('features'))
                                @foreach(old('features') as $index => $feature)
                                    <div class="flex items-center mb-2 feature-row">
                                        <input type="text" 
                                               name="features[]" 
                                               value="{{ $feature }}"
                                               class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="Feature description"
                                               required>
                                        <button type="button" 
                                                class="ml-2 text-red-600 hover:text-red-800 remove-feature"
                                                onclick="removeFeature(this)">
                                            Remove
                                        </button>
                                    </div>
                                @endforeach
                            @else
                                <div class="flex items-center mb-2 feature-row">
                                    <input type="text" 
                                           name="features[]" 
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="Feature description"
                                           required>
                                    <button type="button" 
                                            class="ml-2 text-red-600 hover:text-red-800 remove-feature"
                                            onclick="removeFeature(this)">
                                        Remove
                                    </button>
                                </div>
                            @endif
                        </div>
                        <button type="button" 
                                id="add-feature" 
                                class="mt-2 text-blue-600 hover:text-blue-800 font-medium">
                            + Add Feature
                        </button>
                        @error('features')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Checkboxes -->
                    <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_popular" 
                                   name="is_popular" 
                                   value="1"
                                   {{ old('is_popular') ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_popular" class="ml-2 block text-sm text-gray-900">
                                Mark as Popular
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1"
                                   {{ old('is_active', true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Active
                            </label>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="mt-8 flex justify-end space-x-4">
                        <a href="{{ route('admin.pricing-plans.index') }}" 
                           class="px-6 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-semibold transition-colors duration-200">
                            Create Plan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('add-feature').addEventListener('click', function() {
            const container = document.getElementById('features-container');
            const newRow = document.createElement('div');
            newRow.className = 'flex items-center mb-2 feature-row';
            newRow.innerHTML = `
                <input type="text" 
                       name="features[]" 
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="Feature description"
                       required>
                <button type="button" 
                        class="ml-2 text-red-600 hover:text-red-800 remove-feature"
                        onclick="removeFeature(this)">
                    Remove
                </button>
            `;
            container.appendChild(newRow);
        });

        function removeFeature(button) {
            const container = document.getElementById('features-container');
            if (container.children.length > 1) {
                button.parentElement.remove();
            }
        }
    </script>
</x-layouts.app>
