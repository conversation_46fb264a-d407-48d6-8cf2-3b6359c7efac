<?php

namespace App\Http\Controllers;

use App\Models\Page;
use App\Models\Setting;
use App\Models\Course;
use App\Models\Testimonial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class PageController extends Controller
{
    public function about()
    {
        $page = Page::where('slug', 'about-us')->where('is_active', true)->first();
        
        // Get about settings
        $aboutTitle = Setting::get('about_title', 'About Zajel Arabic Academy');
        $aboutDescription = Setting::get('about_description', 'We are dedicated to teaching Arabic language and Quran to non-Arabic speakers worldwide through innovative online learning methods.');
        $aboutImage = Setting::get('about_image');
        $ourMission = Setting::get('our_mission', 'To make Arabic language and Quran learning accessible to everyone around the world through high-quality online education.');
        $ourVision = Setting::get('our_vision', 'To become the leading platform for Arabic and Quran education, connecting students globally with expert instructors.');

        // Get testimonials
        $testimonials = Testimonial::approved()
            ->featured()
            ->orderBy('created_at', 'desc')
            ->take(6)
            ->get();

        return view('pages.about', compact(
            'page',
            'aboutTitle',
            'aboutDescription',
            'aboutImage',
            'ourMission',
            'ourVision',
            'testimonials'
        ));
    }

    public function contact()
    {
        $page = Page::where('slug', 'contact-us')->where('is_active', true)->first();
        
        // Get contact settings
        $contactEmail = Setting::get('contact_email', '<EMAIL>');
        $contactPhone = Setting::get('contact_phone', '+1234567890');
        $contactAddress = Setting::get('contact_address', '');

        return view('pages.contact', compact('page', 'contactEmail', 'contactPhone', 'contactAddress'));
    }

    public function contactSubmit(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        // Here you would typically send an email
        // For now, we'll just redirect back with a success message
        
        return back()->with('success', 'Thank you for your message! We will get back to you soon.');
    }

    public function pricing()
    {
        $page = Page::where('slug', 'pricing')->where('is_active', true)->first();
        
        // Get courses with pricing
        $courses = Course::where('is_active', true)
            ->with('category')
            ->orderBy('is_featured', 'desc')
            ->orderBy('sort_order')
            ->get();

        return view('pages.pricing', compact('page', 'courses'));
    }

    public function trialClass()
    {
        $page = Page::where('slug', 'trial-class')->where('is_active', true)->first();
        
        // Get featured courses for trial
        $featuredCourses = Course::where('is_active', true)
            ->where('is_featured', true)
            ->with('category')
            ->orderBy('sort_order')
            ->take(6)
            ->get();

        return view('pages.trial-class', compact('page', 'featuredCourses'));
    }

    public function faq()
    {
        $page = Page::where('slug', 'faq')->where('is_active', true)->first();
        
        // You could create a separate FAQ model, but for now we'll use the page content
        $faqs = [
            [
                'question' => 'How do I start learning Arabic?',
                'answer' => 'You can start by enrolling in our beginner Arabic course. We provide step-by-step guidance from basic alphabet to advanced conversation skills.'
            ],
            [
                'question' => 'Are the classes live or recorded?',
                'answer' => 'We offer both live interactive classes and recorded sessions. Live classes allow real-time interaction with instructors, while recorded sessions provide flexibility to learn at your own pace.'
            ],
            [
                'question' => 'What is the duration of each course?',
                'answer' => 'Course duration varies depending on the level and type. Typically, our courses range from 4 weeks to 6 months. Each course page provides detailed information about duration and schedule.'
            ],
            [
                'question' => 'Do you provide certificates?',
                'answer' => 'Yes, we provide certificates of completion for all our courses. These certificates are recognized and can be used for academic or professional purposes.'
            ],
            [
                'question' => 'Can I get a refund if I\'m not satisfied?',
                'answer' => 'We offer a 30-day money-back guarantee. If you\'re not satisfied with the course within the first 30 days, you can request a full refund.'
            ],
            [
                'question' => 'What technical requirements do I need?',
                'answer' => 'You need a stable internet connection, a computer or mobile device, and a microphone for interactive sessions. We recommend using headphones for better audio quality.'
            ]
        ];

        return view('pages.faq', compact('page', 'faqs'));
    }

    public function privacyPolicy()
    {
        $page = Page::where('slug', 'privacy-policy')->where('is_active', true)->first();
        return view('pages.privacy-policy', compact('page'));
    }

    public function termsOfService()
    {
        $page = Page::where('slug', 'terms-of-service')->where('is_active', true)->first();
        return view('pages.terms-of-service', compact('page'));
    }

    public function cookiePolicy()
    {
        $page = Page::where('slug', 'cookie-policy')->where('is_active', true)->first();
        return view('pages.cookie-policy', compact('page'));
    }
}
