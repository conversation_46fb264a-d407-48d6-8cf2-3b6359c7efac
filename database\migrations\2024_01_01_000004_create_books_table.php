<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('books', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->string('author');
            $table->string('image')->nullable();
            $table->string('file_path')->nullable();
            $table->string('file_size')->nullable(); // e.g., "2.5 MB"
            $table->integer('pages')->nullable();
            $table->string('language')->default('Arabic');
            $table->string('isbn')->nullable();
            $table->date('publication_date')->nullable();
            $table->boolean('is_free')->default(true);
            $table->decimal('price', 10, 2)->default(0);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('download_count')->default(0);
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['is_active', 'is_featured', 'sort_order']);
            $table->index(['is_free', 'is_active']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('books');
    }
};
