<?php

namespace App\Http\Controllers;

use App\Models\Book;
use App\Models\BookPurchase;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PaymentController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    public function showCheckout(Book $book)
    {
        if ($book->is_free || !$book->is_active) {
            return redirect()->route('books.show', $book);
        }

        $paymentMethods = $this->paymentService->getAvailablePaymentMethods();

        if (empty($paymentMethods)) {
            return redirect()->route('books.show', $book)
                ->with('error', 'Payment methods are currently unavailable.');
        }

        return view('books.checkout', compact('book', 'paymentMethods'));
    }

    public function processPayment(Request $request, Book $book)
    {
        if ($book->is_free || !$book->is_active) {
            return response()->json(['error' => 'Invalid book for payment'], 400);
        }

        $validator = Validator::make($request->all(), [
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'payment_method' => 'required|in:stripe,paypal',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Create purchase record
            $purchase = $this->paymentService->createPurchase($book, [
                'name' => $request->customer_name,
                'email' => $request->customer_email,
            ]);

            // Process payment based on method
            $result = match($request->payment_method) {
                'stripe' => $this->paymentService->processStripePayment($purchase, $request->all()),
                'paypal' => $this->paymentService->processPayPalPayment($purchase, $request->all()),
                default => ['success' => false, 'error' => 'Invalid payment method']
            };

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'download_url' => route('books.download.purchased', $purchase->download_token),
                    'message' => 'Payment successful! You can now download your book.',
                ]);
            }

            return response()->json($result, 400);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Payment processing failed. Please try again.',
            ], 500);
        }
    }

    public function success($token)
    {
        $purchase = BookPurchase::where('download_token', $token)
            ->where('payment_status', 'completed')
            ->first();

        if (!$purchase) {
            abort(404, 'Purchase not found or payment not completed');
        }

        return view('books.purchase-success', compact('purchase'));
    }

    public function webhook(Request $request, $provider)
    {
        $payload = $request->all();

        $success = $this->paymentService->handleWebhook($provider, $payload);

        return response()->json(['success' => $success]);
    }
}
