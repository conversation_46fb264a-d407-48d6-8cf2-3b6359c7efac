<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('testimonials', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->nullable();
            $table->string('image')->nullable();
            $table->tinyInteger('rating')->default(5); // 1-5 stars
            $table->text('comment');
            $table->string('position')->nullable();
            $table->string('company')->nullable();
            $table->string('country')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_approved')->default(false);
            $table->foreignId('course_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamps();
            
            $table->index(['is_approved', 'is_featured']);
            $table->index(['course_id', 'is_approved']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('testimonials');
    }
};
