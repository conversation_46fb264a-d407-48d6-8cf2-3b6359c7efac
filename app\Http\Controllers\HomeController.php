<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Post;
use App\Models\Testimonial;
use App\Models\Setting;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        // Get featured courses
        $featuredCourses = Course::where('is_featured', true)
            ->where('is_active', true)
            ->with('category')
            ->orderBy('sort_order')
            ->take(6)
            ->get();

        // Get latest blog posts
        $latestPosts = Post::published()
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        // Get featured testimonials
        $testimonials = Testimonial::approved()
            ->featured()
            ->orderBy('created_at', 'desc')
            ->take(6)
            ->get();

        // Get home page settings
        $heroTitle = Setting::get('hero_title', 'Learn Quran & Arabic Language');
        $heroSubtitle = Setting::get('hero_subtitle', 'Master Arabic language and Quran recitation with our expert instructors through interactive online courses designed for non-Arabic speakers.');
        $heroImage = Setting::get('hero_image');
        $heroVideoUrl = Setting::get('hero_video_url');
        
        $aboutTitle = Setting::get('about_title', 'About Zajel Arabic Academy');
        $aboutDescription = Setting::get('about_description', 'We are dedicated to teaching Arabic language and Quran to non-Arabic speakers worldwide through innovative online learning methods.');
        $aboutImage = Setting::get('about_image');
        
        $ourMission = Setting::get('our_mission', 'To make Arabic language and Quran learning accessible to everyone around the world through high-quality online education.');
        $ourVision = Setting::get('our_vision', 'To become the leading platform for Arabic and Quran education, connecting students globally with expert instructors.');

        return view('home', compact(
            'featuredCourses',
            'latestPosts',
            'testimonials',
            'heroTitle',
            'heroSubtitle',
            'heroImage',
            'heroVideoUrl',
            'aboutTitle',
            'aboutDescription',
            'aboutImage',
            'ourMission',
            'ourVision'
        ));
    }
}
