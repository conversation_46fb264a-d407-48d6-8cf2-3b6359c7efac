// Form Handling JavaScript
document.addEventListener('DOMContentLoaded', function() {

    // Contact Form Handler
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';

            // Clear previous errors
            clearFormErrors(this);

            fetch('/contact', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessMessage(data.message);
                    this.reset();
                } else {
                    if (data.errors) {
                        showFormErrors(this, data.errors);
                    } else {
                        showErrorMessage(data.message || 'Something went wrong. Please try again.');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage('Something went wrong. Please try again.');
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    }

    // Trial Class Form Handler
    const trialForm = document.getElementById('trial-form');
    if (trialForm) {
        trialForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Submitting...';

            // Clear previous errors
            clearFormErrors(this);

            fetch('/trial-class', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessMessage(data.message);
                    this.reset();
                } else {
                    if (data.errors) {
                        showFormErrors(this, data.errors);
                    } else {
                        showErrorMessage(data.message || 'Something went wrong. Please try again.');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage('Something went wrong. Please try again.');
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    }
});

// Helper Functions
function showSuccessMessage(message) {
    const alert = createAlert('success', message);

    // Auto remove after 5 seconds
    setTimeout(() => {
        removeAlert(alert);
    }, 5000);
}

function showErrorMessage(message) {
    const alert = createAlert('error', message);

    // Auto remove after 5 seconds
    setTimeout(() => {
        removeAlert(alert);
    }, 5000);
}

function createAlert(type, message) {
    const alert = document.createElement('div');
    alert.className = `fixed top-6 right-6 z-[9999] p-6 rounded-xl shadow-2xl max-w-md transform transition-all duration-300 ease-in-out translate-x-full opacity-0 ${
        type === 'success'
            ? 'bg-gradient-to-r from-green-500 to-green-600 text-white'
            : 'bg-gradient-to-r from-red-500 to-red-600 text-white'
    }`;

    alert.innerHTML = `
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center">
                    <i class="fas ${type === 'success' ? 'fa-check' : 'fa-exclamation-triangle'} text-sm"></i>
                </div>
            </div>
            <div class="ml-4 flex-1">
                <h4 class="text-sm font-semibold mb-1">${type === 'success' ? 'Success!' : 'Error!'}</h4>
                <p class="text-sm opacity-90 leading-relaxed">${message}</p>
            </div>
            <button onclick="removeAlert(this.closest('.fixed'))" class="ml-4 flex-shrink-0 w-6 h-6 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center transition-colors duration-200">
                <i class="fas fa-times text-xs"></i>
            </button>
        </div>
        <div class="absolute bottom-0 left-0 h-1 bg-white/30 rounded-full transition-all duration-5000 ease-linear" style="width: 100%"></div>
    `;

    // Animate in
    document.body.appendChild(alert);
    setTimeout(() => {
        alert.classList.remove('translate-x-full', 'opacity-0');
        alert.classList.add('translate-x-0', 'opacity-100');
    }, 100);

    // Progress bar animation
    const progressBar = alert.querySelector('.absolute.bottom-0');
    setTimeout(() => {
        progressBar.style.width = '0%';
    }, 200);

    return alert;
}

function removeAlert(alert) {
    alert.classList.add('translate-x-full', 'opacity-0');
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 300);
}

function showFormErrors(form, errors) {
    Object.keys(errors).forEach(field => {
        const input = form.querySelector(`[name="${field}"]`);
        if (input) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'text-red-600 text-sm mt-1 form-error';
            errorDiv.textContent = errors[field][0];

            input.classList.add('border-red-500');
            input.parentNode.appendChild(errorDiv);
        }
    });
}

function clearFormErrors(form) {
    // Remove error classes
    form.querySelectorAll('.border-red-500').forEach(input => {
        input.classList.remove('border-red-500');
    });

    // Remove error messages
    form.querySelectorAll('.form-error').forEach(error => {
        error.remove();
    });
}
