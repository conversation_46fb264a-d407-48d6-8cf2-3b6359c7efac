// Form Handling JavaScript
document.addEventListener('DOMContentLoaded', function() {

    // Contact Form Handler
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';

            // Clear previous errors
            clearFormErrors(this);

            fetch('/contact', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessMessage(data.message);
                    this.reset();
                } else {
                    if (data.errors) {
                        showFormErrors(this, data.errors);
                    } else {
                        showErrorMessage(data.message || 'Something went wrong. Please try again.');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage('Something went wrong. Please try again.');
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    }

    // Trial Class Form Handler
    const trialForm = document.getElementById('trial-form');
    if (trialForm) {
        trialForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Submitting...';

            // Clear previous errors
            clearFormErrors(this);

            fetch('/trial-class', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessMessage(data.message);
                    this.reset();
                } else {
                    if (data.errors) {
                        showFormErrors(this, data.errors);
                    } else {
                        showErrorMessage(data.message || 'Something went wrong. Please try again.');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage('Something went wrong. Please try again.');
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    }
});

// Helper Functions
function showSuccessMessage(message) {
    const alert = createAlert('success', message);
    document.body.appendChild(alert);

    // Auto remove after 5 seconds
    setTimeout(() => {
        alert.remove();
    }, 5000);
}

function showErrorMessage(message) {
    const alert = createAlert('error', message);
    document.body.appendChild(alert);

    // Auto remove after 5 seconds
    setTimeout(() => {
        alert.remove();
    }, 5000);
}

function createAlert(type, message) {
    const alert = document.createElement('div');
    alert.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-md ${
        type === 'success'
            ? 'bg-green-100 border border-green-400 text-green-700'
            : 'bg-red-100 border border-red-400 text-red-700'
    }`;

    alert.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-500 hover:text-gray-700">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    return alert;
}

function showFormErrors(form, errors) {
    Object.keys(errors).forEach(field => {
        const input = form.querySelector(`[name="${field}"]`);
        if (input) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'text-red-600 text-sm mt-1 form-error';
            errorDiv.textContent = errors[field][0];

            input.classList.add('border-red-500');
            input.parentNode.appendChild(errorDiv);
        }
    });
}

function clearFormErrors(form) {
    // Remove error classes
    form.querySelectorAll('.border-red-500').forEach(input => {
        input.classList.remove('border-red-500');
    });

    // Remove error messages
    form.querySelectorAll('.form-error').forEach(error => {
        error.remove();
    });
}
