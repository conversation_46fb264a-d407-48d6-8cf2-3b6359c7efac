<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            // Add new columns if they don't exist
            if (!Schema::hasColumn('posts', 'slug')) {
                $table->string('slug')->unique()->after('title');
            }
            if (!Schema::hasColumn('posts', 'excerpt')) {
                $table->text('excerpt')->nullable()->after('content');
            }
            if (!Schema::hasColumn('posts', 'image')) {
                $table->string('image')->nullable()->after('excerpt');
            }
            if (!Schema::hasColumn('posts', 'author_name')) {
                $table->string('author_name')->nullable()->after('image');
            }
            if (!Schema::hasColumn('posts', 'author_bio')) {
                $table->text('author_bio')->nullable()->after('author_name');
            }
            if (!Schema::hasColumn('posts', 'author_image')) {
                $table->string('author_image')->nullable()->after('author_bio');
            }
            if (!Schema::hasColumn('posts', 'is_featured')) {
                $table->boolean('is_featured')->default(false)->after('author_image');
            }
            if (!Schema::hasColumn('posts', 'is_published')) {
                $table->boolean('is_published')->default(false)->after('is_featured');
            }
            if (!Schema::hasColumn('posts', 'published_at')) {
                $table->timestamp('published_at')->nullable()->after('is_published');
            }
            if (!Schema::hasColumn('posts', 'reading_time')) {
                $table->integer('reading_time')->nullable()->after('published_at');
            }
            if (!Schema::hasColumn('posts', 'views_count')) {
                $table->integer('views_count')->default(0)->after('reading_time');
            }
            if (!Schema::hasColumn('posts', 'meta_title')) {
                $table->string('meta_title')->nullable()->after('views_count');
            }
            if (!Schema::hasColumn('posts', 'meta_description')) {
                $table->text('meta_description')->nullable()->after('meta_title');
            }
            if (!Schema::hasColumn('posts', 'meta_keywords')) {
                $table->text('meta_keywords')->nullable()->after('meta_description');
            }
            if (!Schema::hasColumn('posts', 'tags')) {
                $table->json('tags')->nullable()->after('meta_keywords');
            }
            if (!Schema::hasColumn('posts', 'sort_order')) {
                $table->integer('sort_order')->default(0)->after('tags');
            }
            if (!Schema::hasColumn('posts', 'deleted_at')) {
                $table->softDeletes();
            }
        });

        // Add indexes
        Schema::table('posts', function (Blueprint $table) {
            $table->index(['is_published', 'published_at']);
            $table->index(['is_featured', 'is_published']);
        });
    }

    public function down(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->dropColumn([
                'slug', 'excerpt', 'image', 'author_name', 'author_bio', 'author_image',
                'is_featured', 'is_published', 'published_at', 'reading_time', 'views_count',
                'meta_title', 'meta_description', 'meta_keywords', 'tags', 'sort_order'
            ]);
            $table->dropSoftDeletes();
        });
    }
};
