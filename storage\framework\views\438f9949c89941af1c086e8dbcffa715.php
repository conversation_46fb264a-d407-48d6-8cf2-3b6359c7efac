<?php if (isset($component)) { $__componentOriginal5863877a5171c196453bfa0bd807e410 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5863877a5171c196453bfa0bd807e410 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.app','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> Pricing Plans <?php $__env->endSlot(); ?>

    <?php $__env->startPush('head'); ?>
    <style>
        /* Force active state for pricing page */
        .nav-link[href="/pricing"] {
            color: #3b82f6 !important;
            font-weight: 600 !important;
        }

        .nav-link[href="/pricing"]::after {
            content: '' !important;
            position: absolute !important;
            bottom: -8px !important;
            left: 0 !important;
            right: 0 !important;
            height: 3px !important;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8) !important;
            border-radius: 2px !important;
        }
    </style>
    <?php $__env->stopPush(); ?>

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-primary-600 to-primary-800 py-20">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                Choose Your Perfect Plan
            </h1>
            <p class="text-xl text-blue-100 max-w-3xl mx-auto">
                Flexible pricing options designed to fit your learning schedule and budget.
                Start with a free trial and experience the difference.
            </p>
        </div>
    </section>

    <!-- Pricing Plans Section -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">

            <!-- Tab Navigation -->
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-8">
                    Select Your Session Duration
                </h2>
                <div class="inline-flex bg-white rounded-xl p-2 shadow-lg border border-gray-200">
                    <button onclick="switchTab('30min')"
                            id="tab-30min"
                            class="tab-button active px-8 py-4 rounded-lg font-semibold transition-all duration-300 flex items-center">
                        <i class="fas fa-clock mr-2"></i>
                        30 Minutes
                    </button>
                    <button onclick="switchTab('60min')"
                            id="tab-60min"
                            class="tab-button px-8 py-4 rounded-lg font-semibold transition-all duration-300 flex items-center">
                        <i class="fas fa-clock mr-2"></i>
                        60 Minutes
                    </button>
                </div>
            </div>

            <!-- 30 Minutes Plans -->
            <div id="plans-30min" class="tab-content">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <?php $__currentLoopData = $thirtyMinutePlans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="relative bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden transform hover:scale-105 transition-all duration-300 <?php echo e($plan->is_popular ? 'ring-2 ring-blue-500 ring-offset-2' : ''); ?>">
                            <?php if($plan->is_popular): ?>
                                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
                                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-full shadow-lg">
                                        <div class="flex items-center justify-center">
                                            <i class="fas fa-crown mr-2 text-sm"></i>
                                            <span class="text-xs font-bold uppercase tracking-wider">Most Popular</span>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Header -->
                            <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-center py-6">
                                <h3 class="text-xl font-bold text-white"><?php echo e($plan->name); ?></h3>
                            </div>

                            <!-- Price -->
                            <div class="text-center py-8 px-6">
                                <div class="flex items-baseline justify-center mb-2">
                                    <span class="text-lg text-gray-500">$</span>
                                    <span class="text-5xl font-bold text-gray-900"><?php echo e(number_format($plan->price, 0)); ?></span>
                                </div>
                                <p class="text-gray-600 font-medium"><?php echo e($plan->duration); ?></p>
                                <?php if($plan->discount_percentage > 0): ?>
                                    <div class="mt-3">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-percentage mr-1"></i>
                                            <?php echo e($plan->discount_percentage); ?>% OFF
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Features -->
                            <div class="px-6 pb-8">
                                <ul class="space-y-4">
                                    <?php $__currentLoopData = $plan->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="flex items-start">
                                            <div class="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mt-0.5">
                                                <i class="fas fa-check text-green-600 text-xs"></i>
                                            </div>
                                            <span class="ml-3 text-gray-700 font-medium"><?php echo e($feature); ?></span>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>

                            <!-- Button -->
                            <div class="px-6 pb-6">
                                <a href="/trial-class"
                                   class="block w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-center py-4 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl">
                                    Start Free Trial
                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>

            <!-- 60 Minutes Plans -->
            <div id="plans-60min" class="tab-content hidden">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <?php $__currentLoopData = $sixtyMinutePlans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="relative bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden transform hover:scale-105 transition-all duration-300 <?php echo e($plan->is_popular ? 'ring-2 ring-blue-500 ring-offset-2' : ''); ?>">
                            <?php if($plan->is_popular): ?>
                                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
                                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-full shadow-lg">
                                        <div class="flex items-center justify-center">
                                            <i class="fas fa-crown mr-2 text-sm"></i>
                                            <span class="text-xs font-bold uppercase tracking-wider">Most Popular</span>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Header -->
                            <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-center py-6">
                                <h3 class="text-xl font-bold text-white"><?php echo e($plan->name); ?></h3>
                            </div>

                            <!-- Price -->
                            <div class="text-center py-8 px-6">
                                <div class="flex items-baseline justify-center mb-2">
                                    <?php if($plan->hasDiscount()): ?>
                                        <span class="text-lg line-through text-gray-400 mr-2">$<?php echo e(number_format($plan->original_price, 0)); ?></span>
                                    <?php endif; ?>
                                    <span class="text-lg text-gray-500">$</span>
                                    <span class="text-5xl font-bold text-gray-900"><?php echo e(number_format($plan->price, 0)); ?></span>
                                </div>
                                <p class="text-gray-600 font-medium"><?php echo e($plan->duration); ?></p>
                                <?php if($plan->hasDiscount()): ?>
                                    <div class="mt-3">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-percentage mr-1"></i>
                                            Save $<?php echo e(number_format($plan->original_price - $plan->price, 0)); ?>

                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Features -->
                            <div class="px-6 pb-8">
                                <ul class="space-y-4">
                                    <?php $__currentLoopData = $plan->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="flex items-start">
                                            <div class="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mt-0.5">
                                                <i class="fas fa-check text-green-600 text-xs"></i>
                                            </div>
                                            <span class="ml-3 text-gray-700 font-medium"><?php echo e($feature); ?></span>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>

                            <!-- Button -->
                            <div class="px-6 pb-6">
                                <a href="/trial-class"
                                   class="block w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-center py-4 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl">
                                    Start Free Trial
                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Frequently Asked Questions
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Common questions about our pricing and plans
                </p>
            </div>

            <div class="max-w-4xl mx-auto">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8 hover:shadow-xl transition-shadow duration-300">
                        <div class="flex items-start mb-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-exchange-alt text-blue-600"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 ml-4">
                                Can I change my plan anytime?
                            </h3>
                        </div>
                        <p class="text-gray-600 ml-16">
                            Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.
                        </p>
                    </div>

                    <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8 hover:shadow-xl transition-shadow duration-300">
                        <div class="flex items-start mb-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-gift text-green-600"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 ml-4">
                                Is there a free trial available?
                            </h3>
                        </div>
                        <p class="text-gray-600 ml-16">
                            Absolutely! All plans come with a free trial class so you can experience our teaching quality before committing.
                        </p>
                    </div>

                    <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8 hover:shadow-xl transition-shadow duration-300">
                        <div class="flex items-start mb-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-calendar-alt text-orange-600"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 ml-4">
                                What if I need to reschedule a lesson?
                            </h3>
                        </div>
                        <p class="text-gray-600 ml-16">
                            Our 60-minute plans include lesson rescheduling. For 30-minute plans, please contact support for assistance.
                        </p>
                    </div>

                    <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8 hover:shadow-xl transition-shadow duration-300">
                        <div class="flex items-start mb-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-book text-purple-600"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 ml-4">
                                Are the materials really free?
                            </h3>
                        </div>
                        <p class="text-gray-600 ml-16">
                            Yes! All learning materials, including books, worksheets, and digital resources are included at no extra cost.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-br from-blue-600 to-blue-800">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Start Your Learning Journey?
            </h2>
            <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                Join thousands of students who have transformed their Arabic skills with our personalized approach.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/trial-class" class="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200 shadow-lg">
                    <i class="fas fa-play mr-2"></i>
                    Start Free Trial
                </a>
                <a href="/contact" class="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors duration-200">
                    <i class="fas fa-envelope mr-2"></i>
                    Contact Us
                </a>
            </div>
        </div>
    </section>

    <!-- Tab Switching Script -->
    <script>
        (function() {
            'use strict';

            function switchTab(tabName) {
                try {
                    // Hide all tab contents
                    const tabContents = document.querySelectorAll('.tab-content');
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });

                    // Remove active class from all tab buttons
                    const tabButtons = document.querySelectorAll('.tab-button');
                    tabButtons.forEach(button => {
                        button.classList.remove('active', 'bg-gradient-to-r', 'from-blue-600', 'to-blue-700', 'text-white', 'shadow-lg');
                        button.classList.add('text-gray-600', 'bg-transparent');
                    });

                    // Show selected tab content
                    const selectedContent = document.getElementById('plans-' + tabName);
                    if (selectedContent) {
                        selectedContent.classList.remove('hidden');
                    }

                    // Add active class to selected tab button
                    const activeButton = document.getElementById('tab-' + tabName);
                    if (activeButton) {
                        activeButton.classList.add('active', 'bg-gradient-to-r', 'from-blue-600', 'to-blue-700', 'text-white', 'shadow-lg');
                        activeButton.classList.remove('text-gray-600', 'bg-transparent');
                    }
                } catch (error) {
                    console.error('Error switching tab:', error);
                }
            }

            // Make switchTab globally available
            window.switchTab = switchTab;

            // Initialize the first tab as active on page load
            document.addEventListener('DOMContentLoaded', function() {
                try {
                    const firstTab = document.getElementById('tab-30min');
                    if (firstTab) {
                        firstTab.classList.add('active', 'bg-gradient-to-r', 'from-blue-600', 'to-blue-700', 'text-white', 'shadow-lg');
                        firstTab.classList.remove('text-gray-600', 'bg-transparent');
                    }
                } catch (error) {
                    console.error('Error initializing tabs:', error);
                }
            });
        })();
    </script>

    <!-- Tab Styles -->
    <style>
        .tab-button {
            @apply text-gray-600 bg-transparent transition-all duration-300;
        }

        .tab-button.active {
            @apply text-white bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg;
        }

        .tab-button:hover:not(.active) {
            @apply text-blue-600 bg-blue-50;
        }

        .tab-content {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pricing-card:hover {
            transform: translateY(-5px);
        }
    </style>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $attributes = $__attributesOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__attributesOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $component = $__componentOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__componentOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\zajelwebsite\resources\views/pricing.blade.php ENDPATH**/ ?>