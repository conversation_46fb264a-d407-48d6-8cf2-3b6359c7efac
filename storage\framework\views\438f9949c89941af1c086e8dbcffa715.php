<?php if (isset($component)) { $__componentOriginal5863877a5171c196453bfa0bd807e410 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5863877a5171c196453bfa0bd807e410 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.app','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> Pricing Plans <?php $__env->endSlot(); ?>

    <!-- Hero Section -->
    <section class="bg-gradient-primary section-padding">
        <div class="container mx-auto container-padding text-center">
            <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
                Choose Your Perfect Plan
            </h1>
            <p class="text-xl text-blue-100 max-w-3xl mx-auto">
                Flexible pricing options designed to fit your learning schedule and budget.
                Start with a free trial and experience the difference.
            </p>
        </div>
    </section>

    <!-- Pricing Plans Section -->
    <section class="section-padding bg-gray-50">
        <div class="container mx-auto container-padding">

            <!-- Tab Navigation -->
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-8">
                    Select Your Session Duration
                </h2>
                <div class="inline-flex bg-white rounded-xl p-2 shadow-soft border border-gray-200">
                    <button onclick="switchTab('30min')"
                            id="tab-30min"
                            class="tab-button active px-8 py-4 rounded-lg font-semibold transition-all duration-300">
                        <i class="fas fa-clock mr-2"></i>
                        30 Minutes
                    </button>
                    <button onclick="switchTab('60min')"
                            id="tab-60min"
                            class="tab-button px-8 py-4 rounded-lg font-semibold transition-all duration-300">
                        <i class="fas fa-clock mr-2"></i>
                        60 Minutes
                    </button>
                </div>
            </div>

            <!-- 30 Minutes Plans -->
            <div id="plans-30min" class="tab-content">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <?php $__currentLoopData = $thirtyMinutePlans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="relative card card-hover <?php echo e($plan->is_popular ? 'ring-2 ring-primary-500 ring-offset-4' : ''); ?>">
                            <?php if($plan->is_popular): ?>
                                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                                    <span class="bg-gradient-to-r from-accent-500 to-accent-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                                        <i class="fas fa-star mr-1"></i>
                                        POPULAR
                                    </span>
                                </div>
                            <?php endif; ?>

                            <div class="bg-gradient-to-r from-primary-600 to-primary-700 text-center py-6 -mx-6 -mt-6 mb-8 rounded-t-xl">
                                <h3 class="text-xl font-bold text-white"><?php echo e($plan->name); ?></h3>
                            </div>

                            <div class="text-center mb-8 px-6">
                                <div class="flex items-baseline justify-center mb-2">
                                    <span class="text-lg text-gray-500">$</span>
                                    <span class="text-5xl font-bold text-gray-900"><?php echo e(number_format($plan->price, 0)); ?></span>
                                </div>
                                <p class="text-gray-600 font-medium"><?php echo e($plan->duration); ?></p>
                                <?php if($plan->discount_percentage > 0): ?>
                                    <div class="mt-2">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-percentage mr-1"></i>
                                            <?php echo e($plan->discount_percentage); ?>% OFF
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="px-6 mb-8">
                                <ul class="space-y-4">
                                    <?php $__currentLoopData = $plan->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="flex items-start">
                                            <div class="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mt-0.5">
                                                <i class="fas fa-check text-green-600 text-xs"></i>
                                            </div>
                                            <span class="ml-3 text-gray-700 font-medium"><?php echo e($feature); ?></span>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>

                            <div class="px-6 pb-6">
                                <a href="<?php echo e($plan->button_url); ?>"
                                   class="btn btn-primary w-full text-center">
                                    <?php echo e($plan->button_text); ?>

                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>

            <!-- 60 Minutes Plans -->
            <div id="plans-60min" class="tab-content hidden">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <?php $__currentLoopData = $sixtyMinutePlans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="relative card card-hover <?php echo e($plan->is_popular ? 'ring-2 ring-primary-500 ring-offset-4' : ''); ?>">
                            <?php if($plan->is_popular): ?>
                                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                                    <span class="bg-gradient-to-r from-accent-500 to-accent-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                                        <i class="fas fa-star mr-1"></i>
                                        POPULAR
                                    </span>
                                </div>
                            <?php endif; ?>

                            <div class="bg-gradient-to-r from-secondary-700 to-secondary-800 text-center py-6 -mx-6 -mt-6 mb-8 rounded-t-xl">
                                <h3 class="text-xl font-bold text-white"><?php echo e($plan->name); ?></h3>
                            </div>

                            <div class="text-center mb-8 px-6">
                                <div class="flex items-baseline justify-center mb-2">
                                    <?php if($plan->hasDiscount()): ?>
                                        <span class="text-lg line-through text-gray-400 mr-2">$<?php echo e(number_format($plan->original_price, 0)); ?></span>
                                    <?php endif; ?>
                                    <span class="text-lg text-gray-500">$</span>
                                    <span class="text-5xl font-bold text-gray-900"><?php echo e(number_format($plan->price, 0)); ?></span>
                                </div>
                                <p class="text-gray-600 font-medium"><?php echo e($plan->duration); ?></p>
                                <?php if($plan->hasDiscount()): ?>
                                    <div class="mt-2">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-tag mr-1"></i>
                                            Save $<?php echo e(number_format($plan->original_price - $plan->price, 0)); ?>

                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="px-6 mb-8">
                                <ul class="space-y-4">
                                    <?php $__currentLoopData = $plan->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="flex items-start">
                                            <div class="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mt-0.5">
                                                <i class="fas fa-check text-green-600 text-xs"></i>
                                            </div>
                                            <span class="ml-3 text-gray-700 font-medium"><?php echo e($feature); ?></span>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>

                            <div class="px-6 pb-6">
                                <a href="<?php echo e($plan->button_url); ?>"
                                   class="btn btn-primary w-full text-center">
                                    <?php echo e($plan->button_text); ?>

                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="section-padding bg-white">
        <div class="container mx-auto container-padding">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Pricing FAQ
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Common questions about our pricing and plans
                </p>
            </div>

            <div class="max-w-4xl mx-auto">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="card p-8">
                        <div class="flex items-start mb-4">
                            <div class="flex-shrink-0 w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-exchange-alt text-primary-600"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 ml-4">
                                Can I change my plan anytime?
                            </h3>
                        </div>
                        <p class="text-gray-600 ml-14">
                            Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.
                        </p>
                    </div>

                    <div class="card p-8">
                        <div class="flex items-start mb-4">
                            <div class="flex-shrink-0 w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-gift text-green-600"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 ml-4">
                                Is there a free trial available?
                            </h3>
                        </div>
                        <p class="text-gray-600 ml-14">
                            Absolutely! All plans come with a free trial class so you can experience our teaching quality before committing.
                        </p>
                    </div>

                    <div class="card p-8">
                        <div class="flex items-start mb-4">
                            <div class="flex-shrink-0 w-10 h-10 bg-accent-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-calendar-alt text-accent-600"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 ml-4">
                                What if I need to reschedule a lesson?
                            </h3>
                        </div>
                        <p class="text-gray-600 ml-14">
                            Our 60-minute plans include lesson rescheduling. For 30-minute plans, please contact support for assistance.
                        </p>
                    </div>

                    <div class="card p-8">
                        <div class="flex items-start mb-4">
                            <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-book text-blue-600"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 ml-4">
                                Are the materials really free?
                            </h3>
                        </div>
                        <p class="text-gray-600 ml-14">
                            Yes! All learning materials, including books, worksheets, and digital resources are included at no extra cost.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section-padding bg-gradient-primary">
        <div class="container mx-auto container-padding text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Start Your Learning Journey?
            </h2>
            <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                Join thousands of students who have transformed their Arabic skills with our personalized approach.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/trial-class" class="btn bg-white text-primary-600 hover:bg-gray-100">
                    <i class="fas fa-play mr-2"></i>
                    Start Free Trial
                </a>
                <a href="/contact" class="btn btn-outline border-white text-white hover:bg-white hover:text-primary-600">
                    <i class="fas fa-envelope mr-2"></i>
                    Contact Us
                </a>
            </div>
        </div>
    </section>

    <!-- Tab Switching Script -->
    <script>
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById('plans-' + tabName).classList.remove('hidden');

            // Add active class to selected tab button
            document.getElementById('tab-' + tabName).classList.add('active');
        }
    </script>

    <!-- Tab Styles -->
    <style>
        .tab-button {
            @apply text-gray-600 bg-transparent;
        }

        .tab-button.active {
            @apply text-white bg-gradient-to-r from-primary-600 to-primary-700 shadow-lg;
        }

        .tab-button:hover:not(.active) {
            @apply text-primary-600 bg-primary-50;
        }

        .tab-content {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $attributes = $__attributesOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__attributesOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $component = $__componentOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__componentOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\zajelwebsite\resources\views/pricing.blade.php ENDPATH**/ ?>