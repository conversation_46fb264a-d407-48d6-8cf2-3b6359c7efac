<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Category;
use Illuminate\Http\Request;

class CourseController extends Controller
{
    public function index(Request $request)
    {
        $query = Course::where('is_active', true)->with('category');

        // Filter by category
        if ($request->has('category') && $request->category) {
            $category = Category::where('slug', $request->category)->first();
            if ($category) {
                $query->where('category_id', $category->id);
            }
        }

        // Filter by level
        if ($request->has('level') && $request->level) {
            $query->where('level', $request->level);
        }

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('instructor_name', 'like', "%{$search}%");
            });
        }

        // Sort
        $sortBy = $request->get('sort', 'featured');
        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'featured':
            default:
                $query->orderBy('is_featured', 'desc')->orderBy('sort_order');
                break;
        }

        $courses = $query->paginate(12);
        
        // Get categories for filter
        $categories = Category::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        // Get levels for filter
        $levels = [
            'beginner' => 'Beginner',
            'intermediate' => 'Intermediate',
            'advanced' => 'Advanced'
        ];

        return view('courses.index', compact('courses', 'categories', 'levels'));
    }

    public function show(Course $course)
    {
        // Check if course is active
        if (!$course->is_active) {
            abort(404);
        }

        // Load relationships
        $course->load('category', 'testimonials.approved');

        // Get related courses
        $relatedCourses = Course::where('is_active', true)
            ->where('category_id', $course->category_id)
            ->where('id', '!=', $course->id)
            ->take(3)
            ->get();

        // Get course testimonials
        $testimonials = $course->testimonials()
            ->approved()
            ->orderBy('created_at', 'desc')
            ->take(6)
            ->get();

        return view('courses.show', compact('course', 'relatedCourses', 'testimonials'));
    }
}
