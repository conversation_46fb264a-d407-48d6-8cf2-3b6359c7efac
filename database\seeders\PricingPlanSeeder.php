<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PricingPlan;

class PricingPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 30 Minutes Plans
        PricingPlan::create([
            'name' => '2 Days per Week',
            'price' => 40,
            'original_price' => null,
            'discount_percentage' => 0,
            'duration' => 'per month',
            'features' => [
                '0% OFF',
                'Free Trial class',
                'One to one lessons',
                '8 Classes Monthly',
                'Free materials'
            ],
            'button_text' => 'Start Free Trial',
            'button_url' => '#',
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 1
        ]);

        PricingPlan::create([
            'name' => '3 Days per Week',
            'price' => 58,
            'original_price' => null,
            'discount_percentage' => 3,
            'duration' => 'per month',
            'features' => [
                '3% OFF',
                'Free Trial class',
                'One to one lessons',
                '12 Classes Monthly',
                'Free materials'
            ],
            'button_text' => 'Start Free Trial',
            'button_url' => '#',
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 2
        ]);

        PricingPlan::create([
            'name' => '4 Days per Week',
            'price' => 76,
            'original_price' => null,
            'discount_percentage' => 5,
            'duration' => 'per month',
            'features' => [
                '5% OFF',
                'Free Trial class',
                'One to one lessons',
                '16 Classes Monthly',
                'Free materials'
            ],
            'button_text' => 'Start Free Trial',
            'button_url' => '#',
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 3
        ]);

        PricingPlan::create([
            'name' => '5 Days per Week',
            'price' => 90,
            'original_price' => null,
            'discount_percentage' => 10,
            'duration' => 'per month',
            'features' => [
                '10% OFF',
                'Free Trial class',
                'One to one lessons',
                '20 Classes Monthly',
                'Free materials'
            ],
            'button_text' => 'Start Free Trial',
            'button_url' => '#',
            'is_popular' => true,
            'is_active' => true,
            'sort_order' => 4
        ]);

        // 60 Minutes Plans
        PricingPlan::create([
            'name' => '2 Days per Week',
            'price' => 80,
            'original_price' => null,
            'discount_percentage' => 0,
            'duration' => 'per month',
            'features' => [
                'Free Trial class',
                'Reschedule Lessons',
                'One to one lessons',
                '8 Classes Monthly',
                'Free materials'
            ],
            'button_text' => 'Start Free Trial',
            'button_url' => '#',
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 5
        ]);

        PricingPlan::create([
            'name' => '3 Days per Week',
            'price' => 115,
            'original_price' => 120,
            'discount_percentage' => 0,
            'duration' => 'per month',
            'features' => [
                'Free Trial class',
                'Reschedule Lessons',
                'One to one lessons',
                '12 Classes Monthly',
                'Free materials'
            ],
            'button_text' => 'Start Free Trial',
            'button_url' => '#',
            'is_popular' => true,
            'is_active' => true,
            'sort_order' => 6
        ]);

        PricingPlan::create([
            'name' => '4 Days per Week',
            'price' => 150,
            'original_price' => 160,
            'discount_percentage' => 0,
            'duration' => 'per month',
            'features' => [
                'Free Trial class',
                'Reschedule Lessons',
                'One to one lessons',
                '16 Classes Monthly',
                'Free materials'
            ],
            'button_text' => 'Start Free Trial',
            'button_url' => '#',
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 7
        ]);

        PricingPlan::create([
            'name' => '5 Days per Week',
            'price' => 190,
            'original_price' => 200,
            'discount_percentage' => 0,
            'duration' => 'per month',
            'features' => [
                'Free Trial class',
                'Reschedule Lessons',
                'One to one Lessons',
                '20 Classes Monthly',
                'Free materials'
            ],
            'button_text' => 'Start Free Trial',
            'button_url' => '#',
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 8
        ]);
    }
}
