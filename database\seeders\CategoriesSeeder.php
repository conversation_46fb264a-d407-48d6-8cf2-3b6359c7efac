<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategoriesSeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Arabic Language',
                'slug' => 'arabic-language',
                'description' => 'Learn Arabic language from basics to advanced levels with our comprehensive courses.',
                'icon' => 'fas fa-language',
                'color' => '#1E40AF',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Quran Memorization',
                'slug' => 'quran-memorization',
                'description' => 'Memorize the Holy Quran with proper Tajweed and understanding.',
                'icon' => 'fas fa-book-quran',
                'color' => '#059669',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Quran Recitation',
                'slug' => 'quran-recitation',
                'description' => 'Perfect your Quran recitation with proper pronunciation and Tajweed rules.',
                'icon' => 'fas fa-microphone',
                'color' => '#7C3AED',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Islamic Studies',
                'slug' => 'islamic-studies',
                'description' => 'Comprehensive Islamic education covering various aspects of Islamic knowledge.',
                'icon' => 'fas fa-mosque',
                'color' => '#DC2626',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Tajweed',
                'slug' => 'tajweed',
                'description' => 'Master the art of Tajweed for beautiful and correct Quran recitation.',
                'icon' => 'fas fa-music',
                'color' => '#EA580C',
                'is_active' => true,
                'sort_order' => 5,
            ],
        ];

        foreach ($categories as $category) {
            Category::updateOrCreate(
                ['slug' => $category['slug']],
                $category
            );
        }
    }
}
