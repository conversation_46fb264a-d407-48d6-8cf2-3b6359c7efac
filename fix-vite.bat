@echo off
echo Fixing Vite Assets for Zajel Arabic Academy...
cd /d "d:\laragon\www\zajelwebsite"

echo.
echo [1/4] Installing Node.js dependencies...
call npm install

echo.
echo [2/4] Building production assets...
call npm run build

echo.
echo [3/4] Creating storage link...
php artisan storage:link

echo.
echo [4/4] Clearing caches...
php artisan cache:clear
php artisan config:clear
php artisan view:clear

echo.
echo ✅ Vite assets fixed successfully!
echo.
echo The website should now work without manifest errors.
echo Start the server with: php artisan serve
echo.
pause
