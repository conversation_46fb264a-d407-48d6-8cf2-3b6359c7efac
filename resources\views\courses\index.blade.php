<x-layouts.app>
    <x-slot name="title">Our Courses - Zajel Arabic Academy</x-slot>
    <x-slot name="metaDescription">Explore our comprehensive Arabic language and Quran courses designed for non-Arabic speakers. Learn with expert instructors through interactive online classes.</x-slot>

    <!-- Page Header -->
    <section class="bg-gradient-primary text-white py-16 md:py-20">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                    Our Courses
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed font-medium">
                    Discover our comprehensive collection of Arabic language and Quran courses designed to help you achieve your learning goals.
                </p>
            </div>
        </div>
    </section>

    <!-- Filters Section -->
    <section class="bg-white border-b border-gray-200 sticky top-16 md:top-20 z-40">
        <div class="max-w-7xl mx-auto container-padding py-6">
            <form method="GET" class="flex flex-wrap items-center gap-4">
                <!-- Search -->
                <div class="flex-1 min-w-64">
                    <div class="relative">
                        <input 
                            type="text" 
                            name="search" 
                            value="{{ request('search') }}"
                            placeholder="Search courses..." 
                            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium"
                        >
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <!-- Category Filter -->
                <select name="category" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium">
                    <option value="">All Categories</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->slug }}" {{ request('category') === $category->slug ? 'selected' : '' }}>
                            {{ $category->name }}
                        </option>
                    @endforeach
                </select>

                <!-- Level Filter -->
                <select name="level" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium">
                    <option value="">All Levels</option>
                    @foreach($levels as $value => $label)
                        <option value="{{ $value }}" {{ request('level') === $value ? 'selected' : '' }}>
                            {{ $label }}
                        </option>
                    @endforeach
                </select>

                <!-- Sort -->
                <select name="sort" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium">
                    <option value="featured" {{ request('sort') === 'featured' ? 'selected' : '' }}>Featured</option>
                    <option value="newest" {{ request('sort') === 'newest' ? 'selected' : '' }}>Newest</option>
                    <option value="price_low" {{ request('sort') === 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                    <option value="price_high" {{ request('sort') === 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                </select>

                <!-- Filter Button -->
                <button type="submit" class="btn btn-primary font-semibold px-6 py-3">
                    <i class="fas fa-filter mr-2"></i>
                    Filter
                </button>

                <!-- Clear Filters -->
                @if(request()->hasAny(['search', 'category', 'level', 'sort']))
                    <a href="/courses" class="btn btn-outline font-semibold px-6 py-3">
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </a>
                @endif
            </form>
        </div>
    </section>

    <!-- Courses Grid -->
    <section class="section-padding bg-gray-50">
        <div class="max-w-7xl mx-auto container-padding">
            @if($courses->count() > 0)
                <!-- Results Info -->
                <div class="flex items-center justify-between mb-8">
                    <p class="text-gray-600 font-medium">
                        Showing {{ $courses->firstItem() }}-{{ $courses->lastItem() }} of {{ $courses->total() }} courses
                    </p>
                </div>

                <!-- Courses Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                    @foreach($courses as $course)
                        <div class="card card-hover group">
                            <!-- Course Image -->
                            <div class="relative overflow-hidden">
                                @if($course->image)
                                    <img 
                                        src="{{ Storage::url($course->image) }}" 
                                        alt="{{ $course->title }}"
                                        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                    >
                                @else
                                    <div class="w-full h-48 bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center">
                                        <i class="fas fa-book-open text-white text-3xl"></i>
                                    </div>
                                @endif
                                
                                <!-- Course Level Badge -->
                                <div class="absolute top-4 left-4">
                                    <span class="px-3 py-1 bg-white text-primary-600 text-sm font-semibold rounded-full shadow-md">
                                        {{ ucfirst($course->level) }}
                                    </span>
                                </div>
                                
                                <!-- Course Price -->
                                @if($course->price > 0)
                                    <div class="absolute top-4 right-4">
                                        <div class="bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                            @if($course->discount_price)
                                                <span class="line-through opacity-75">${{ number_format($course->price, 0) }}</span>
                                                <span class="ml-1">${{ number_format($course->discount_price, 0) }}</span>
                                            @else
                                                ${{ number_format($course->price, 0) }}
                                            @endif
                                        </div>
                                    </div>
                                @else
                                    <div class="absolute top-4 right-4">
                                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                            Free
                                        </span>
                                    </div>
                                @endif

                                @if($course->is_featured)
                                    <div class="absolute bottom-4 left-4">
                                        <span class="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold">
                                            <i class="fas fa-star mr-1"></i>
                                            Featured
                                        </span>
                                    </div>
                                @endif
                            </div>

                            <!-- Course Content -->
                            <div class="p-6">
                                <!-- Category -->
                                <div class="flex items-center mb-3">
                                    <span class="text-sm font-medium text-primary-600 bg-primary-50 px-3 py-1 rounded-full">
                                        {{ $course->category->name }}
                                    </span>
                                </div>

                                <!-- Course Title -->
                                <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-200">
                                    {{ $course->title }}
                                </h3>

                                <!-- Course Description -->
                                <p class="text-gray-600 mb-4 leading-relaxed font-medium">
                                    {{ Str::limit($course->short_description ?: $course->description, 100) }}
                                </p>

                                <!-- Course Meta -->
                                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-clock mr-2"></i>
                                        <span class="font-medium">{{ $course->duration ?: '8 weeks' }}</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-user mr-2"></i>
                                        <span class="font-medium">{{ $course->instructor_name }}</span>
                                    </div>
                                </div>

                                <!-- Course Link -->
                                <a href="/courses/{{ $course->slug }}" class="block w-full btn btn-primary text-center font-semibold">
                                    <i class="fas fa-arrow-right mr-2"></i>
                                    View Course
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="flex justify-center">
                    {{ $courses->appends(request()->query())->links() }}
                </div>
            @else
                <!-- No Results -->
                <div class="text-center py-16">
                    <i class="fas fa-search text-6xl text-gray-300 mb-6"></i>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">No courses found</h3>
                    <p class="text-lg text-gray-600 mb-8 max-w-md mx-auto">
                        We couldn't find any courses matching your criteria. Try adjusting your filters or search terms.
                    </p>
                    <a href="/courses" class="btn btn-primary font-semibold">
                        <i class="fas fa-refresh mr-2"></i>
                        View All Courses
                    </a>
                </div>
            @endif
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section-padding bg-gradient-primary">
        <div class="max-w-4xl mx-auto container-padding text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Can't Find What You're Looking For?
            </h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed font-medium">
                Contact us to discuss your specific learning needs and we'll help you find the perfect course.
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/contact" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                    <i class="fas fa-envelope mr-3"></i>
                    Contact Us
                </a>
                <a href="/trial-class" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 text-lg font-semibold px-8 py-4">
                    <i class="fas fa-play-circle mr-3"></i>
                    Try Free Class
                </a>
            </div>
        </div>
    </section>
</x-layouts.app>
