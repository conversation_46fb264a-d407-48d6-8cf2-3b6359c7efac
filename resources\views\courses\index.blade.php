<x-layouts.app>
    <x-slot name="title">Our Courses - Zajel Arabic Academy</x-slot>
    <x-slot name="metaDescription">Explore our comprehensive Arabic language and Quran courses designed for non-Arabic speakers. Learn with expert instructors through interactive online classes.</x-slot>

    <!-- Page Header -->
    <section class="relative bg-gradient-to-br from-blue-800 via-blue-900 to-indigo-900 text-white py-20 md:py-28 overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
        <div class="absolute bottom-20 right-10 w-32 h-32 bg-purple-400/20 rounded-full blur-xl"></div>

        <div class="max-w-7xl mx-auto container-padding relative">
            <div class="text-center">
                <div class="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-sm rounded-full text-sm font-semibold mb-8">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    Explore Our Courses
                </div>
                <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight">
                    Master <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-cyan-300">Arabic</span> & <span class="text-transparent bg-clip-text bg-gradient-to-r from-indigo-300 to-blue-300">Quran</span>
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed font-medium">
                    Discover our comprehensive collection of Arabic language and Quran courses designed to help you achieve your learning goals with expert guidance.
                </p>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 max-w-2xl mx-auto">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-300">{{ $courses->total() }}+</div>
                        <div class="text-blue-200 text-sm font-medium">Courses</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-cyan-300">50+</div>
                        <div class="text-blue-200 text-sm font-medium">Instructors</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-indigo-300">5000+</div>
                        <div class="text-blue-200 text-sm font-medium">Students</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-sky-300">100+</div>
                        <div class="text-blue-200 text-sm font-medium">Countries</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Filters Section -->
    <section class="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-40 shadow-lg">
        <div class="max-w-7xl mx-auto container-padding py-8">
            <form method="GET" class="space-y-6">
                <!-- Search Bar -->
                <div class="relative max-w-2xl mx-auto">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400 text-lg"></i>
                    </div>
                    <input
                        type="text"
                        name="search"
                        value="{{ request('search') }}"
                        placeholder="Search for courses, topics, or instructors..."
                        class="w-full pl-12 pr-4 py-4 text-lg border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
                    >
                </div>

                <!-- Filter Pills -->
                <div class="flex flex-wrap items-center justify-center gap-4">
                    <!-- Category Filter -->
                    <div class="relative">
                        <select name="category" class="appearance-none bg-white border-2 border-gray-200 rounded-full px-6 py-3 pr-10 text-sm font-semibold focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 hover:border-gray-300">
                            <option value="">All Categories</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->slug }}" {{ request('category') === $category->slug ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        <i class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                    </div>

                    <!-- Level Filter -->
                    <div class="relative">
                        <select name="level" class="appearance-none bg-white border-2 border-gray-200 rounded-full px-6 py-3 pr-10 text-sm font-semibold focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 hover:border-gray-300">
                            <option value="">All Levels</option>
                            @foreach($levels as $value => $label)
                                <option value="{{ $value }}" {{ request('level') === $value ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                        <i class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                    </div>

                    <!-- Sort Filter -->
                    <div class="relative">
                        <select name="sort" class="appearance-none bg-white border-2 border-gray-200 rounded-full px-6 py-3 pr-10 text-sm font-semibold focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 hover:border-gray-300">
                            <option value="featured" {{ request('sort') === 'featured' ? 'selected' : '' }}>Featured</option>
                            <option value="newest" {{ request('sort') === 'newest' ? 'selected' : '' }}>Newest</option>
                            <option value="price_low" {{ request('sort') === 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                            <option value="price_high" {{ request('sort') === 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                        </select>
                        <i class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                    </div>

                    <!-- Filter Button -->
                    <button type="submit" class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-8 py-3 rounded-full font-semibold hover:from-blue-700 hover:to-blue-800 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="fas fa-filter mr-2"></i>
                        Apply Filters
                    </button>

                    <!-- Clear Filters -->
                    @if(request()->hasAny(['search', 'category', 'level', 'sort']))
                        <a href="/courses" class="bg-gray-100 text-gray-700 px-6 py-3 rounded-full font-semibold hover:bg-gray-200 transition-all duration-200">
                            <i class="fas fa-times mr-2"></i>
                            Clear All
                        </a>
                    @endif
                </div>
            </form>
        </div>
    </section>

    <!-- Courses Grid -->
    <section class="py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50">
        <div class="max-w-7xl mx-auto container-padding">
            @if($courses->count() > 0)
                <!-- Results Info -->
                <div class="flex items-center justify-between mb-12">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Available Courses</h2>
                        <p class="text-gray-600 font-medium">
                            Showing {{ $courses->firstItem() }}-{{ $courses->lastItem() }} of {{ $courses->total() }} courses
                        </p>
                    </div>
                    <div class="hidden md:flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-info-circle"></i>
                        <span>Click on any course to learn more</span>
                    </div>
                </div>

                <!-- Courses Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                    @foreach($courses as $course)
                        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl hover:border-blue-200 transition-all duration-300 group">
                                <!-- Course Image -->
                                <div class="relative overflow-hidden">
                                    @if($course->image)
                                        <img
                                            src="{{ Storage::url($course->image) }}"
                                            alt="{{ $course->title }}"
                                            class="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-700"
                                        >
                                    @else
                                        <div class="w-full h-56 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 flex items-center justify-center">
                                            <div class="text-center text-white">
                                                <i class="fas fa-book-open text-4xl mb-2"></i>
                                                <p class="text-lg font-semibold">{{ $course->category->name ?? 'Course' }}</p>
                                            </div>
                                        </div>
                                    @endif



                                    <!-- Course Level Badge -->
                                    <div class="absolute top-4 left-4">
                                        <span class="px-3 py-1.5 bg-white text-blue-600 text-sm font-bold rounded-full shadow-lg border border-blue-100">
                                            {{ ucfirst($course->level ?? 'Beginner') }}
                                        </span>
                                    </div>

                                    <!-- Course Price -->
                                    @if($course->price > 0)
                                        <div class="absolute top-4 right-4">
                                            <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                                                @if($course->discount_price)
                                                    <span class="line-through opacity-75">${{ number_format($course->price, 0) }}</span>
                                                    <span class="ml-1">${{ number_format($course->discount_price, 0) }}</span>
                                                @else
                                                    ${{ number_format($course->price, 0) }}
                                                @endif
                                            </div>
                                        </div>
                                    @else
                                        <div class="absolute top-4 right-4">
                                            <span class="bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                                                <i class="fas fa-gift mr-1"></i>Free
                                            </span>
                                        </div>
                                    @endif

                                    <!-- Featured Badge -->
                                    @if($course->is_featured)
                                        <div class="absolute bottom-4 left-4">
                                            <span class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                                                <i class="fas fa-star mr-1"></i>Featured
                                            </span>
                                        </div>
                                    @endif
                                </div>

                                @if($course->is_featured)
                                    <div class="absolute bottom-4 left-4">
                                        <span class="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold">
                                            <i class="fas fa-star mr-1"></i>
                                            Featured
                                        </span>
                                    </div>
                                @endif
                            </div>

                                <!-- Course Content -->
                                <div class="p-8">
                                    <!-- Category -->
                                    @if($course->category)
                                        <div class="flex items-center mb-4">
                                            <span class="text-sm font-semibold text-blue-600 bg-blue-50 px-4 py-2 rounded-full border border-blue-100">
                                                <i class="fas fa-tag mr-2"></i>{{ $course->category->name }}
                                            </span>
                                        </div>
                                    @endif

                                    <!-- Course Title -->
                                    <h3 class="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-200 leading-tight">
                                        {{ $course->title }}
                                    </h3>

                                    <!-- Course Description -->
                                    <p class="text-gray-600 mb-6 leading-relaxed font-medium text-base">
                                        {{ Str::limit(strip_tags($course->short_description ?: $course->description), 120) }}
                                    </p>

                                    <!-- Course Meta -->
                                    <div class="grid grid-cols-2 gap-4 mb-6">
                                        <div class="flex items-center text-sm text-gray-500">
                                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-clock text-blue-600"></i>
                                            </div>
                                            <div>
                                                <p class="font-semibold text-gray-900">Duration</p>
                                                <p class="text-xs">{{ $course->duration ?: '8 weeks' }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center text-sm text-gray-500">
                                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-users text-green-600"></i>
                                            </div>
                                            <div>
                                                <p class="font-semibold text-gray-900">Students</p>
                                                <p class="text-xs">{{ $course->max_students ?: 'Unlimited' }}</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Course Link -->
                                    <a href="{{ route('courses.show', $course->slug) }}" class="block w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white text-center font-bold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-blue-800 transform hover:scale-105 transition-all duration-200">
                                        <i class="fas fa-graduation-cap mr-3"></i>
                                        View Course Details
                                        <i class="fas fa-arrow-right ml-3"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="flex justify-center">
                    {{ $courses->appends(request()->query())->links() }}
                </div>
            @else
                <!-- No Results -->
                <div class="text-center py-16">
                    <i class="fas fa-search text-6xl text-gray-300 mb-6"></i>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">No courses found</h3>
                    <p class="text-lg text-gray-600 mb-8 max-w-md mx-auto">
                        We couldn't find any courses matching your criteria. Try adjusting your filters or search terms.
                    </p>
                    <a href="/courses" class="btn btn-primary font-semibold">
                        <i class="fas fa-refresh mr-2"></i>
                        View All Courses
                    </a>
                </div>
            @endif
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-24 bg-gradient-to-br from-blue-800 via-blue-900 to-indigo-900 relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-10 left-10 w-24 h-24 bg-white/10 rounded-full blur-xl animate-pulse"></div>
        <div class="absolute bottom-10 right-10 w-32 h-32 bg-yellow-400/20 rounded-full blur-xl animate-pulse" style="animation-delay: 1s;"></div>

        <div class="max-w-6xl mx-auto container-padding text-center relative">
            <!-- Icon -->
            <div class="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-8">
                <i class="fas fa-search text-white text-2xl"></i>
            </div>

            <h2 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-8 leading-tight">
                Can't Find What You're <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-orange-300">Looking For</span>?
            </h2>
            <p class="text-xl md:text-2xl text-blue-100 mb-12 leading-relaxed font-medium max-w-3xl mx-auto">
                Don't worry! Our expert team is here to help you find the perfect course that matches your learning goals and schedule.
            </p>

            <!-- Features Grid -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <div class="text-center">
                    <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user-tie text-white text-xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Personal Consultation</h3>
                    <p class="text-blue-200 text-sm">Get personalized course recommendations</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-calendar-alt text-white text-xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Flexible Scheduling</h3>
                    <p class="text-blue-200 text-sm">Find courses that fit your schedule</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-graduation-cap text-white text-xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Custom Courses</h3>
                    <p class="text-blue-200 text-sm">We can create courses just for you</p>
                </div>
            </div>

            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-6 justify-center">
                <a href="/contact" class="group relative bg-white text-blue-600 hover:text-blue-700 text-lg font-bold px-10 py-5 rounded-2xl shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300 overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-r from-gray-50 to-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <span class="relative z-10 flex items-center justify-center">
                        <i class="fas fa-envelope mr-3"></i>
                        Contact Our Team
                        <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </span>
                </a>
                <a href="/trial-class" class="group relative bg-transparent text-white border-2 border-white hover:bg-white hover:text-blue-600 text-lg font-bold px-10 py-5 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300">
                    <span class="flex items-center justify-center">
                        <i class="fas fa-play-circle mr-3"></i>
                        Try Free Class
                        <i class="fas fa-rocket ml-3 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </span>
                </a>
            </div>
        </div>
    </section>
</x-layouts.app>
