# 🔐 Zajel Arabic Academy - Login Information

## 🛠️ Admin Dashboard Access

### Default Admin Credentials (After Seeding)
- **URL**: http://localhost:8000/admin
- **Email**: <EMAIL>
- **Password**: password123

### Creating New Admin User
If you need to create a new admin user, run:
```bash
php artisan make:filament-user
```

Or double-click: `create-admin.bat`

## 🌐 Website URLs

### Public Website
- **Home**: http://localhost:8000
- **Courses**: http://localhost:8000/courses
- **Blog**: http://localhost:8000/blog
- **Books**: http://localhost:8000/books
- **About**: http://localhost:8000/about
- **Contact**: http://localhost:8000/contact
- **Pricing**: http://localhost:8000/pricing
- **Trial Class**: http://localhost:8000/trial-class
- **FAQ**: http://localhost:8000/faq

### Admin Panel
- **Dashboard**: http://localhost:8000/admin
- **Courses Management**: http://localhost:8000/admin/courses
- **Blog Management**: http://localhost:8000/admin/posts
- **Books Management**: http://localhost:8000/admin/books
- **Testimonials**: http://localhost:8000/admin/testimonials
- **Categories**: http://localhost:8000/admin/categories
- **Settings**: http://localhost:8000/admin/settings
- **Users**: http://localhost:8000/admin/users

## 🔧 Troubleshooting Login Issues

### If you can't login to admin panel:

1. **Reset Admin Password**:
   ```bash
   php artisan tinker
   ```
   Then run:
   ```php
   $user = App\Models\User::where('email', '<EMAIL>')->first();
   $user->password = bcrypt('newpassword123');
   $user->save();
   ```

2. **Create New Admin User**:
   ```bash
   php artisan make:filament-user
   ```

3. **Check Database Connection**:
   - Verify `.env` database settings
   - Make sure database exists
   - Run `php artisan migrate:fresh --seed`

### If website shows errors:

1. **Clear Caches**:
   ```bash
   php artisan cache:clear
   php artisan config:clear
   php artisan view:clear
   ```

2. **Regenerate Key**:
   ```bash
   php artisan key:generate
   ```

3. **Check File Permissions**:
   - Make sure `storage/` and `bootstrap/cache/` are writable

## 📊 Sample Data Included

After running the seeder, you'll have:
- ✅ **5 Course Categories**
- ✅ **5 Complete Courses**
- ✅ **3 Blog Posts**
- ✅ **10 Student Testimonials**
- ✅ **8 Free Books**
- ✅ **1 Admin User**
- ✅ **Complete Site Settings**

## 🚀 Quick Start Commands

### Full Setup:
```bash
# Run migrations and seed data
php artisan migrate:fresh --seed

# Create admin user (if needed)
php artisan make:filament-user

# Start development server
php artisan serve
```

### Or use batch files:
- **Complete Setup**: Double-click `optimize.bat`
- **Create Admin**: Double-click `create-admin.bat`
- **Development**: Double-click `dev-start.bat`

## 📞 Support

If you're still having issues:
- Check the Laravel logs in `storage/logs/laravel.log`
- Verify your `.env` configuration
- Make sure all dependencies are installed with `composer install`

---

**🎉 Happy coding with Zajel Arabic Academy!**
