<x-layouts.app>
    <x-slot name="title">Pricing Plans</x-slot>

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Choose Your Perfect Plan
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Flexible pricing options designed to fit your learning schedule and budget. 
                Start with a free trial and experience the difference.
            </p>
        </div>
    </section>

    <!-- Pricing Plans Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            
            <!-- 30 Minutes Plans -->
            <div class="mb-16">
                <div class="text-center mb-12">
                    <div class="inline-flex items-center bg-amber-100 text-amber-800 px-6 py-3 rounded-full mb-4">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        30 Minutes
                    </div>
                    <h2 class="text-3xl font-bold text-gray-900">Perfect for Beginners</h2>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    @foreach($thirtyMinutePlans as $plan)
                        <div class="relative bg-gray-800 rounded-2xl p-8 text-white {{ $plan->is_popular ? 'ring-4 ring-amber-400' : '' }}">
                            @if($plan->is_popular)
                                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                    <span class="bg-amber-400 text-gray-900 px-4 py-2 rounded-full text-sm font-bold">
                                        POPULAR
                                    </span>
                                </div>
                            @endif
                            
                            <div class="bg-amber-600 text-center py-4 -mx-8 -mt-8 mb-8 rounded-t-2xl">
                                <h3 class="text-xl font-bold">{{ $plan->name }}</h3>
                            </div>

                            <div class="text-center mb-8">
                                <div class="flex items-baseline justify-center">
                                    <span class="text-sm">$</span>
                                    <span class="text-5xl font-bold">{{ number_format($plan->price, 0) }}</span>
                                </div>
                                <p class="text-gray-300 mt-2">{{ $plan->duration }}</p>
                            </div>

                            <ul class="space-y-4 mb-8">
                                @foreach($plan->features as $feature)
                                    <li class="flex items-center">
                                        <svg class="w-5 h-5 text-green-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm">{{ $feature }}</span>
                                    </li>
                                @endforeach
                            </ul>

                            <a href="{{ $plan->button_url }}" 
                               class="block w-full bg-amber-600 hover:bg-amber-700 text-center py-3 rounded-lg font-semibold transition-colors duration-200">
                                {{ $plan->button_text }}
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- 60 Minutes Plans -->
            <div>
                <div class="text-center mb-12">
                    <div class="inline-flex items-center bg-gray-100 text-gray-800 px-6 py-3 rounded-full mb-4">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        60 Minutes
                    </div>
                    <h2 class="text-3xl font-bold text-gray-900">For Serious Learners</h2>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    @foreach($sixtyMinutePlans as $plan)
                        <div class="relative bg-gray-800 rounded-2xl p-8 text-white {{ $plan->is_popular ? 'ring-4 ring-amber-400' : '' }}">
                            @if($plan->is_popular)
                                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                    <span class="bg-amber-400 text-gray-900 px-4 py-2 rounded-full text-sm font-bold">
                                        POPULAR
                                    </span>
                                </div>
                            @endif
                            
                            <div class="bg-amber-600 text-center py-4 -mx-8 -mt-8 mb-8 rounded-t-2xl">
                                <h3 class="text-xl font-bold">{{ $plan->name }}</h3>
                            </div>

                            <div class="text-center mb-8">
                                <div class="flex items-baseline justify-center">
                                    @if($plan->hasDiscount())
                                        <span class="text-sm line-through text-gray-400 mr-2">${{ number_format($plan->original_price, 0) }}</span>
                                    @endif
                                    <span class="text-sm">$</span>
                                    <span class="text-5xl font-bold">{{ number_format($plan->price, 0) }}</span>
                                </div>
                                <p class="text-gray-300 mt-2">{{ $plan->duration }}</p>
                            </div>

                            <ul class="space-y-4 mb-8">
                                @foreach($plan->features as $feature)
                                    <li class="flex items-center">
                                        <svg class="w-5 h-5 text-green-400 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm">{{ $feature }}</span>
                                    </li>
                                @endforeach
                            </ul>

                            <a href="{{ $plan->button_url }}" 
                               class="block w-full bg-amber-600 hover:bg-amber-700 text-center py-3 rounded-lg font-semibold transition-colors duration-200">
                                {{ $plan->button_text }}
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Pricing FAQ
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Common questions about our pricing and plans
                </p>
            </div>

            <div class="max-w-4xl mx-auto">
                <div class="space-y-6">
                    <div class="bg-white rounded-lg p-6 shadow-sm">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">
                            Can I change my plan anytime?
                        </h3>
                        <p class="text-gray-600">
                            Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.
                        </p>
                    </div>

                    <div class="bg-white rounded-lg p-6 shadow-sm">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">
                            Is there a free trial available?
                        </h3>
                        <p class="text-gray-600">
                            Absolutely! All plans come with a free trial class so you can experience our teaching quality before committing.
                        </p>
                    </div>

                    <div class="bg-white rounded-lg p-6 shadow-sm">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">
                            What if I need to reschedule a lesson?
                        </h3>
                        <p class="text-gray-600">
                            Our 60-minute plans include lesson rescheduling. For 30-minute plans, please contact support for assistance.
                        </p>
                    </div>

                    <div class="bg-white rounded-lg p-6 shadow-sm">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">
                            Are the materials really free?
                        </h3>
                        <p class="text-gray-600">
                            Yes! All learning materials, including books, worksheets, and digital resources are included at no extra cost.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-blue-600 to-indigo-700">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Start Your Learning Journey?
            </h2>
            <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                Join thousands of students who have transformed their Arabic skills with our personalized approach.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="#" class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200">
                    Start Free Trial
                </a>
                <a href="#" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors duration-200">
                    Contact Us
                </a>
            </div>
        </div>
    </section>
</x-layouts.app>
