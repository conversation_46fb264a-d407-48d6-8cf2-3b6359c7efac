# 🚀 Zajel Arabic Academy - Deployment Checklist

## ✅ Pre-Deployment Checklist

### 🔧 Environment Setup
- [ ] Copy `.env.example` to `.env`
- [ ] Set `APP_ENV=production`
- [ ] Set `APP_DEBUG=false`
- [ ] Generate `APP_KEY` with `php artisan key:generate`
- [ ] Configure database credentials
- [ ] Set up mail configuration
- [ ] Configure file storage (local/S3)

### 🗄️ Database Setup
- [ ] Create production database
- [ ] Run `php artisan migrate --force`
- [ ] Run `php artisan db:seed --force` (optional for sample data)
- [ ] Create admin user: `php artisan make:filament-user`

### 🎨 Assets & Storage
- [ ] Run `npm run build` for production assets
- [ ] Create storage link: `php artisan storage:link`
- [ ] Set proper file permissions (755 for directories, 644 for files)
- [ ] Ensure `storage/` and `bootstrap/cache/` are writable

### ⚡ Performance Optimization
- [ ] Run `php artisan config:cache`
- [ ] Run `php artisan route:cache`
- [ ] Run `php artisan view:cache`
- [ ] Run `composer install --optimize-autoloader --no-dev`

### 🛡️ Security Configuration
- [ ] Set strong database passwords
- [ ] Configure HTTPS/SSL certificates
- [ ] Set up firewall rules
- [ ] Configure backup strategy
- [ ] Set up monitoring and logging

## 🌐 Server Requirements

### Minimum Requirements
- **PHP**: 8.2 or higher
- **MySQL**: 5.7+ or PostgreSQL 10+
- **Web Server**: Apache 2.4+ or Nginx 1.15+
- **Memory**: 512MB RAM minimum (1GB+ recommended)
- **Storage**: 1GB+ free space

### PHP Extensions Required
- BCMath
- Ctype
- Fileinfo
- JSON
- Mbstring
- OpenSSL
- PDO
- Tokenizer
- XML
- GD or Imagick (for image processing)

## 📁 Directory Structure for Production

```
/var/www/zajelarabic.com/
├── public/                 # Document root
├── app/
├── config/
├── database/
├── resources/
├── routes/
├── storage/               # Writable
│   ├── app/
│   ├── framework/
│   └── logs/
├── bootstrap/cache/       # Writable
└── vendor/
```

## 🔒 File Permissions

```bash
# Set ownership
chown -R www-data:www-data /var/www/zajelarabic.com

# Set directory permissions
find /var/www/zajelarabic.com -type d -exec chmod 755 {} \;

# Set file permissions
find /var/www/zajelarabic.com -type f -exec chmod 644 {} \;

# Set writable directories
chmod -R 775 storage bootstrap/cache
```

## 🌐 Web Server Configuration

### Apache (.htaccess)
```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteRule ^(.*)$ public/$1 [L]
</IfModule>
```

### Nginx
```nginx
server {
    listen 80;
    server_name zajelarabic.com www.zajelarabic.com;
    root /var/www/zajelarabic.com/public;
    
    index index.php;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

## 📧 Email Configuration

### SMTP Settings (.env)
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Zajel Arabic Academy"
```

## 🔄 Backup Strategy

### Database Backup
```bash
# Daily backup script
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql
```

### File Backup
```bash
# Backup uploaded files
tar -czf files_backup_$(date +%Y%m%d).tar.gz storage/app/public/
```

## 📊 Monitoring & Maintenance

### Log Monitoring
- Monitor `storage/logs/laravel.log`
- Set up log rotation
- Configure error alerting

### Performance Monitoring
- Monitor database performance
- Track page load times
- Monitor server resources

### Regular Maintenance
- [ ] Update dependencies monthly
- [ ] Review and clean logs weekly
- [ ] Database optimization monthly
- [ ] Security updates immediately

## 🚨 Troubleshooting

### Common Issues
1. **500 Error**: Check file permissions and `.env` configuration
2. **Database Connection**: Verify credentials and server access
3. **Missing Assets**: Run `npm run build` and check public/build/
4. **Storage Issues**: Ensure storage link exists and permissions are correct

### Debug Mode (Temporary)
```env
APP_DEBUG=true
LOG_LEVEL=debug
```
**Remember to disable debug mode in production!**

## 📞 Support Contacts

- **Technical Support**: <EMAIL>
- **Emergency Contact**: [Your emergency contact]
- **Hosting Provider**: [Your hosting provider support]

---

**🎉 Ready for production deployment!**
