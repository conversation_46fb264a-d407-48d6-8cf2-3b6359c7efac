<x-layouts.app>
    <x-slot name="title">Checkout - {{ $book->title }} | Zajel Arabic Academy</x-slot>
    <x-slot name="metaDescription">Complete your purchase of {{ $book->title }} securely with our payment system.</x-slot>

    <!-- Checkout Section -->
    <section class="py-16 bg-gray-50 min-h-screen">
        <div class="max-w-4xl mx-auto container-padding">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">Complete Your Purchase</h1>
                <p class="text-xl text-gray-600">Secure checkout for your digital book</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Order Summary -->
                <div class="bg-white rounded-2xl shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Order Summary</h2>
                    
                    <div class="flex items-start space-x-4 mb-6">
                        @if($book->image)
                            <img src="{{ Storage::url($book->image) }}" alt="{{ $book->title }}" class="w-20 h-28 object-cover rounded-lg">
                        @else
                            <div class="w-20 h-28 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg flex items-center justify-center">
                                <i class="fas fa-book text-white text-2xl"></i>
                            </div>
                        @endif
                        
                        <div class="flex-1">
                            <h3 class="text-lg font-bold text-gray-900 mb-2">{{ $book->title }}</h3>
                            <p class="text-gray-600 mb-2">by {{ $book->author }}</p>
                            @if($book->pages)
                                <p class="text-sm text-gray-500">{{ $book->pages }} pages</p>
                            @endif
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-6">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-gray-600">Book Price:</span>
                            <span class="text-lg font-semibold text-gray-900">${{ number_format($book->price, 2) }}</span>
                        </div>
                        <div class="flex justify-between items-center text-xl font-bold text-gray-900 border-t border-gray-200 pt-4">
                            <span>Total:</span>
                            <span>${{ number_format($book->price, 2) }}</span>
                        </div>
                    </div>

                    <!-- Security Badge -->
                    <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-shield-alt text-green-600 text-xl mr-3"></i>
                            <div>
                                <p class="text-green-800 font-semibold">Secure Payment</p>
                                <p class="text-green-600 text-sm">Your payment information is encrypted and secure</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Form -->
                <div class="bg-white rounded-2xl shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Payment Information</h2>
                    
                    <form id="payment-form" class="space-y-6">
                        @csrf
                        
                        <!-- Customer Information -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-900">Customer Information</h3>
                            
                            <div>
                                <label for="customer_name" class="block text-sm font-semibold text-gray-700 mb-2">Full Name</label>
                                <input type="text" id="customer_name" name="customer_name" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            
                            <div>
                                <label for="customer_email" class="block text-sm font-semibold text-gray-700 mb-2">Email Address</label>
                                <input type="email" id="customer_email" name="customer_email" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <p class="text-sm text-gray-500 mt-1">Download link will be sent to this email</p>
                            </div>
                        </div>

                        <!-- Payment Methods -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-900">Payment Method</h3>
                            
                            @foreach($paymentMethods as $method => $details)
                                <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 transition-colors">
                                    <input type="radio" name="payment_method" value="{{ $method }}" class="mr-4" required>
                                    <i class="{{ $details['icon'] }} text-xl mr-4 text-gray-600"></i>
                                    <div>
                                        <div class="font-semibold text-gray-900">{{ $details['name'] }}</div>
                                        <div class="text-sm text-gray-500">{{ $details['description'] }}</div>
                                    </div>
                                </label>
                            @endforeach
                        </div>

                        <!-- Stripe Card Element (will be shown when Stripe is selected) -->
                        <div id="stripe-card-section" class="hidden space-y-4">
                            <h3 class="text-lg font-semibold text-gray-900">Card Information</h3>
                            <div id="card-element" class="p-4 border border-gray-300 rounded-lg">
                                <!-- Stripe Elements will create form elements here -->
                            </div>
                            <div id="card-errors" role="alert" class="text-red-600 text-sm"></div>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" id="submit-payment" 
                            class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold py-4 px-6 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                            <span id="button-text">
                                <i class="fas fa-lock mr-2"></i>
                                Complete Purchase - ${{ number_format($book->price, 2) }}
                            </span>
                            <span id="button-loading" class="hidden">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Processing...
                            </span>
                        </button>
                    </form>

                    <!-- Terms -->
                    <div class="mt-6 text-center">
                        <p class="text-sm text-gray-500">
                            By completing this purchase, you agree to our 
                            <a href="/terms" class="text-blue-600 hover:underline">Terms of Service</a> and 
                            <a href="/privacy" class="text-blue-600 hover:underline">Privacy Policy</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    @push('head')
        @if(in_array('stripe', array_keys($paymentMethods)))
            <script src="https://js.stripe.com/v3/"></script>
        @endif
    @endpush

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const form = document.getElementById('payment-form');
                const submitButton = document.getElementById('submit-payment');
                const buttonText = document.getElementById('button-text');
                const buttonLoading = document.getElementById('button-loading');
                
                // Payment method selection
                const paymentMethodInputs = document.querySelectorAll('input[name="payment_method"]');
                const stripeCardSection = document.getElementById('stripe-card-section');
                
                let stripe, elements, cardElement;
                
                // Initialize Stripe if available
                @if(in_array('stripe', array_keys($paymentMethods)))
                    // You would get this from your payment settings
                    stripe = Stripe('{{ config("services.stripe.key") }}');
                    elements = stripe.elements();
                    cardElement = elements.create('card');
                @endif
                
                paymentMethodInputs.forEach(input => {
                    input.addEventListener('change', function() {
                        if (this.value === 'stripe') {
                            stripeCardSection.classList.remove('hidden');
                            if (cardElement && !cardElement._mounted) {
                                cardElement.mount('#card-element');
                            }
                        } else {
                            stripeCardSection.classList.add('hidden');
                        }
                    });
                });
                
                // Form submission
                form.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    
                    const formData = new FormData(form);
                    const paymentMethod = formData.get('payment_method');
                    
                    // Show loading state
                    submitButton.disabled = true;
                    buttonText.classList.add('hidden');
                    buttonLoading.classList.remove('hidden');
                    
                    try {
                        if (paymentMethod === 'stripe') {
                            await handleStripePayment(formData);
                        } else if (paymentMethod === 'paypal') {
                            await handlePayPalPayment(formData);
                        }
                    } catch (error) {
                        console.error('Payment error:', error);
                        alert('Payment failed. Please try again.');
                    } finally {
                        // Reset button state
                        submitButton.disabled = false;
                        buttonText.classList.remove('hidden');
                        buttonLoading.classList.add('hidden');
                    }
                });
                
                async function handleStripePayment(formData) {
                    const {token, error} = await stripe.createToken(cardElement);
                    
                    if (error) {
                        document.getElementById('card-errors').textContent = error.message;
                        return;
                    }
                    
                    formData.append('stripe_token', token.id);
                    
                    const response = await fetch('{{ route("books.payment", $book) }}', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        }
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        window.location.href = result.download_url;
                    } else {
                        alert(result.error || 'Payment failed');
                    }
                }
                
                async function handlePayPalPayment(formData) {
                    // PayPal integration would go here
                    alert('PayPal integration coming soon!');
                }
            });
        </script>
    @endpush
</x-layouts.app>
