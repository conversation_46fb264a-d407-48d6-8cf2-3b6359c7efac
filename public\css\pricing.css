/* Pricing Page Custom Styles */

/* Pricing Cards */
.pricing-card {
    transition: all 0.3s ease;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.pricing-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
}

.pricing-card.popular {
    border: 2px solid #3b82f6;
    position: relative;
    transform: scale(1.05);
}

.pricing-card.popular:hover {
    transform: translateY(-8px) scale(1.07);
}

.pricing-card.popular::before {
    content: "Most Popular";
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 8px 24px;
    border-radius: 25px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    z-index: 10;
}

/* Tab Buttons */
.tab-container {
    background: white;
    border-radius: 12px;
    padding: 4px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.tab-button {
    padding: 12px 32px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    color: #6b7280;
    background: transparent;
    border: none;
    cursor: pointer;
}

.tab-button:hover:not(.active) {
    background: #eff6ff;
    color: #3b82f6;
}

.tab-button.active {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* FAQ Cards */
.faq-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.faq-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Buttons */
.btn-primary-custom {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 16px 32px;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    text-decoration: none;
    display: inline-block;
}

.btn-primary-custom:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.6);
    color: white;
    text-decoration: none;
}

/* Feature Lists */
.feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
}

.feature-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #dcfce7;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    margin-top: 2px;
    flex-shrink: 0;
}

.feature-icon i {
    color: #16a34a;
    font-size: 10px;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .pricing-card.popular {
        transform: none;
    }

    .pricing-card.popular:hover {
        transform: translateY(-4px);
    }

    .tab-button {
        padding: 10px 20px;
        font-size: 14px;
    }

    .pricing-card {
        margin-bottom: 24px;
    }
}

@media (max-width: 640px) {
    .tab-button {
        padding: 8px 16px;
        font-size: 13px;
    }
}

/* Price Display */
.price-display {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1;
    color: #1f2937;
}

.price-currency {
    font-size: 1.25rem;
    color: #6b7280;
    vertical-align: top;
}

.price-period {
    font-size: 1rem;
    color: #6b7280;
    font-weight: 500;
}

/* Discount Badge */
.discount-badge {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    margin-top: 8px;
}

.discount-badge i {
    margin-right: 4px;
}

/* Header Gradients */
.card-header-blue {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.card-header-purple {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.card-header-green {
    background: linear-gradient(135deg, #10b981, #059669);
}

.card-header-orange {
    background: linear-gradient(135deg, #f97316, #ea580c);
}

/* About & Contact Page Styles */
.page-section {
    padding: 5rem 0;
}

.contact-form {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    padding: 2rem;
}

.contact-info-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.contact-info-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.value-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    padding: 2rem;
    transition: all 0.3s ease;
}

.value-card:hover {
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    transform: translateY(-8px);
}

/* Form Styles */
.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
    border-color: #ef4444;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .page-section {
        padding: 3rem 0;
    }

    .contact-form,
    .value-card {
        padding: 1.5rem;
    }

    .contact-info-card {
        padding: 1rem;
    }
}
