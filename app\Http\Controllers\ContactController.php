<?php

namespace App\Http\Controllers;

use App\Models\ContactSubmission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class ContactController extends Controller
{
    public function store(Request $request)
    {
        // Rate limiting - prevent spam
        $key = 'contact_' . $request->ip();
        if (cache()->has($key)) {
            return response()->json([
                'success' => false,
                'message' => 'Please wait before sending another message. Try again in 3 minutes.',
            ], 429);
        }

        // Honeypot check - if filled, it's a bot
        if ($request->filled('website')) {
            Log::warning('Bot detected via honeypot', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Your message could not be sent. Please try again.',
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|regex:/^[a-zA-Z\s]+$/',
            'email' => 'required|email:rfc,dns|max:255',
            'phone' => 'nullable|string|max:20|regex:/^[+]?[0-9\s\-\(\)]+$/',
            'subject' => 'required|string|max:255|min:5',
            'message' => 'required|string|max:2000|min:10',
            'website' => 'nullable|max:0', // Honeypot should be empty
            'g-recaptcha-response' => 'nullable', // For future reCAPTCHA implementation
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please check your input and try again.',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check for duplicate messages
        $existingMessage = ContactSubmission::where('email', $request->email)
            ->where('message', $request->message)
            ->where('created_at', '>=', now()->subHours(1))
            ->first();

        if ($existingMessage) {
            return response()->json([
                'success' => false,
                'message' => 'You have already sent this message recently. Please wait before sending again.',
            ], 409);
        }

        // Simple spam detection
        $spamWords = ['viagra', 'casino', 'lottery', 'winner', 'congratulations', 'click here', 'free money'];
        $messageText = strtolower($request->message . ' ' . $request->subject);

        foreach ($spamWords as $word) {
            if (strpos($messageText, $word) !== false) {
                Log::warning('Potential spam detected', [
                    'ip' => $request->ip(),
                    'email' => $request->email,
                    'message' => $request->message
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Your message could not be sent. Please contact us directly.',
                ], 400);
            }
        }

        try {
            $contact = ContactSubmission::create([
                'name' => trim($request->name),
                'email' => strtolower(trim($request->email)),
                'phone' => $request->phone ? preg_replace('/[^+0-9]/', '', $request->phone) : null,
                'subject' => trim($request->subject),
                'message' => trim($request->message),
                'is_read' => false,
            ]);

            // Set rate limit cache (3 minutes)
            cache()->put($key, true, now()->addMinutes(3));

            return response()->json([
                'success' => true,
                'message' => 'Thank you! Your message has been sent successfully. We will get back to you within 24 hours.',
                'data' => [
                    'id' => $contact->id,
                    'name' => $contact->name,
                    'email' => $contact->email,
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('Contact form error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong. Please try again or contact us directly.',
            ], 500);
        }
    }
}
