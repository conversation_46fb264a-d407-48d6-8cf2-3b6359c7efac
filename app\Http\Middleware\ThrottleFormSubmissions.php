<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class ThrottleFormSubmissions
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only apply to form submissions
        if (!$request->isMethod('POST')) {
            return $next($request);
        }

        $key = 'form_submission_' . $request->ip() . '_' . $request->path();

        // Check if IP has submitted recently
        if (Cache::has($key)) {
            return response()->json([
                'success' => false,
                'message' => 'Too many requests. Please wait before submitting again.',
            ], 429);
        }

        // Set cache for 2 minutes
        Cache::put($key, true, now()->addMinutes(2));

        return $next($request);
    }
}
