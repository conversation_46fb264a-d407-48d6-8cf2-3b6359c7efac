<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class BookPurchase extends Model
{
    use HasFactory;

    protected $fillable = [
        'book_id',
        'customer_email',
        'customer_name',
        'amount',
        'currency',
        'payment_provider',
        'payment_id',
        'payment_status',
        'payment_data',
        'download_token',
        'expires_at',
        'download_count',
        'max_downloads',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_data' => 'array',
        'expires_at' => 'datetime',
        'download_count' => 'integer',
        'max_downloads' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($purchase) {
            if (empty($purchase->download_token)) {
                $purchase->download_token = Str::random(64);
            }
            if (empty($purchase->expires_at)) {
                $purchase->expires_at = now()->addDays(30); // 30 days to download
            }
        });
    }

    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    public function canDownload(): bool
    {
        return $this->payment_status === 'completed'
            && $this->download_count < $this->max_downloads
            && $this->expires_at > now();
    }

    public function incrementDownloadCount(): void
    {
        $this->increment('download_count');
    }

    public function scopeCompleted($query)
    {
        return $query->where('payment_status', 'completed');
    }

    public function scopeForCustomer($query, string $email)
    {
        return $query->where('customer_email', $email);
    }
}
