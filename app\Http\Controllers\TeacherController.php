<?php

namespace App\Http\Controllers;

use App\Models\Teacher;
use Illuminate\Http\Request;

class TeacherController extends Controller
{
    public function index()
    {
        $featuredTeachers = Teacher::active()
            ->featured()
            ->ordered()
            ->take(6)
            ->get();

        $allTeachers = Teacher::active()
            ->where('is_featured', false)
            ->ordered()
            ->get();

        return view('pages.teachers', compact('featuredTeachers', 'allTeachers'));
    }

    public function show(Teacher $teacher)
    {
        if (!$teacher->is_active) {
            abort(404);
        }

        return view('pages.teacher-profile', compact('teacher'));
    }
}
