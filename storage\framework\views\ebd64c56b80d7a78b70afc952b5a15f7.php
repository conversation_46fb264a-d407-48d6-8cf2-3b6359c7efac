<?php if (isset($component)) { $__componentOriginal5863877a5171c196453bfa0bd807e410 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5863877a5171c196453bfa0bd807e410 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.app','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> <?php echo e($book->title); ?> - Free Download | Zajel Arabic Academy <?php $__env->endSlot(); ?>
     <?php $__env->slot('metaDescription', null, []); ?> <?php echo e($book->meta_description ?: Str::limit($book->description, 160)); ?> <?php $__env->endSlot(); ?>

    <!-- Book Hero Section -->
    <section class="bg-gradient-primary text-white py-16 md:py-20">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Book Info -->
                <div>
                    <!-- Breadcrumb -->
                    <nav class="flex items-center space-x-2 text-blue-200 mb-6">
                        <a href="/" class="hover:text-white transition-colors font-medium">Home</a>
                        <i class="fas fa-chevron-right text-xs"></i>
                        <a href="/books" class="hover:text-white transition-colors font-medium">Books</a>
                        <i class="fas fa-chevron-right text-xs"></i>
                        <span class="text-white font-medium"><?php echo e(Str::limit($book->title, 30)); ?></span>
                    </nav>

                    <h1 class="text-4xl md:text-5xl font-bold mb-4 leading-tight">
                        <?php echo e($book->title); ?>

                    </h1>

                    <p class="text-xl text-blue-100 mb-6 leading-relaxed font-medium">
                        by <?php echo e($book->author); ?>

                    </p>

                    <p class="text-lg text-blue-100 mb-8 leading-relaxed font-medium">
                        <?php echo e($book->description); ?>

                    </p>

                    <!-- Book Meta -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                        <?php if($book->pages): ?>
                            <div class="text-center">
                                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-file-alt text-lg"></i>
                                </div>
                                <div class="text-sm text-blue-200 font-medium">Pages</div>
                                <div class="font-semibold"><?php echo e($book->pages); ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if($book->language): ?>
                            <div class="text-center">
                                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-language text-lg"></i>
                                </div>
                                <div class="text-sm text-blue-200 font-medium">Language</div>
                                <div class="font-semibold"><?php echo e($book->language); ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if($book->file_size): ?>
                            <div class="text-center">
                                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-download text-lg"></i>
                                </div>
                                <div class="text-sm text-blue-200 font-medium">File Size</div>
                                <div class="font-semibold"><?php echo e($book->file_size); ?></div>
                            </div>
                        <?php endif; ?>

                        <div class="text-center">
                            <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-users text-lg"></i>
                            </div>
                            <div class="text-sm text-blue-200 font-medium">Downloads</div>
                            <div class="font-semibold"><?php echo e(number_format($book->download_count)); ?></div>
                        </div>
                    </div>

                    <!-- Download Button -->
                    <?php if($book->file_path): ?>
                        <a href="/books/<?php echo e($book->slug); ?>/download" class="inline-flex items-center justify-center bg-white text-blue-600 hover:bg-gray-50 hover:text-blue-700 text-lg font-bold px-8 py-4 rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-200">
                            <i class="fas fa-download mr-3"></i>
                            <?php if($book->is_free): ?>
                                Download Free
                            <?php else: ?>
                                Buy & Download - $<?php echo e(number_format($book->price, 0)); ?>

                            <?php endif; ?>
                        </a>
                    <?php else: ?>
                        <div class="inline-flex items-center justify-center bg-gray-400 text-white text-lg font-bold px-8 py-4 rounded-xl cursor-not-allowed">
                            <i class="fas fa-clock mr-3"></i>
                            Coming Soon
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Book Cover -->
                <div class="relative">
                    <?php if($book->image): ?>
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl">
                            <img
                                src="<?php echo e(Storage::url($book->image)); ?>"
                                alt="<?php echo e($book->title); ?>"
                                class="w-full h-96 md:h-[500px] object-cover"
                            >
                        </div>
                    <?php else: ?>
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl bg-white/10 backdrop-blur-sm border border-white/20">
                            <div class="flex items-center justify-center h-96 md:h-[500px]">
                                <div class="text-center text-white">
                                    <i class="fas fa-book text-6xl mb-4 opacity-80"></i>
                                    <p class="text-xl font-semibold"><?php echo e($book->title); ?></p>
                                    <p class="text-blue-200 mt-2">by <?php echo e($book->author); ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Price Badge -->
                    <div class="absolute top-4 right-4">
                        <?php if($book->is_free): ?>
                            <div class="bg-green-500 text-white px-4 py-2 rounded-full font-bold shadow-lg">
                                <span class="text-lg">Free</span>
                            </div>
                        <?php else: ?>
                            <div class="bg-primary-600 text-white px-4 py-2 rounded-full font-bold shadow-lg">
                                <span class="text-lg">$<?php echo e(number_format($book->price, 0)); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Book Details -->
    <section class="py-20 bg-white">
        <div class="max-w-4xl mx-auto container-padding">
            <!-- Book Description -->
            <div class="mb-20">
                <div class="text-center mb-12">
                    <h2 class="text-4xl font-bold text-gray-900 mb-4">About This Book</h2>
                    <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-blue-700 mx-auto rounded-full"></div>
                </div>
                <div class="prose prose-xl max-w-none text-gray-700 leading-relaxed font-medium bg-gradient-to-br from-gray-50 to-blue-50 rounded-3xl p-12 shadow-lg border border-gray-100">
                    <?php echo nl2br(e($book->description)); ?>

                </div>
            </div>

            <!-- Book Details Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
                <!-- Book Information -->
                <div class="card p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Book Information</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600 font-medium">Title:</span>
                            <span class="text-gray-900 font-semibold"><?php echo e($book->title); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 font-medium">Author:</span>
                            <span class="text-gray-900 font-semibold"><?php echo e($book->author); ?></span>
                        </div>
                        <?php if($book->isbn): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600 font-medium">ISBN:</span>
                                <span class="text-gray-900 font-semibold"><?php echo e($book->isbn); ?></span>
                            </div>
                        <?php endif; ?>
                        <?php if($book->publication_date): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600 font-medium">Published:</span>
                                <span class="text-gray-900 font-semibold"><?php echo e($book->publication_date->format('Y')); ?></span>
                            </div>
                        <?php endif; ?>
                        <?php if($book->pages): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600 font-medium">Pages:</span>
                                <span class="text-gray-900 font-semibold"><?php echo e($book->pages); ?></span>
                            </div>
                        <?php endif; ?>
                        <div class="flex justify-between">
                            <span class="text-gray-600 font-medium">Language:</span>
                            <span class="text-gray-900 font-semibold"><?php echo e($book->language); ?></span>
                        </div>
                        <?php if($book->file_size): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600 font-medium">File Size:</span>
                                <span class="text-gray-900 font-semibold"><?php echo e($book->file_size); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Download Stats -->
                <div class="card p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Download Information</h3>
                    <div class="space-y-4">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-primary-600 mb-2"><?php echo e(number_format($book->download_count)); ?></div>
                            <div class="text-gray-600 font-medium">Total Downloads</div>
                        </div>

                        <?php if($book->is_free): ?>
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                                <i class="fas fa-gift text-green-600 text-2xl mb-2"></i>
                                <div class="text-green-800 font-semibold">Free Download</div>
                                <div class="text-green-600 text-sm font-medium">No registration required</div>
                            </div>
                        <?php else: ?>
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                                <i class="fas fa-star text-blue-600 text-2xl mb-2"></i>
                                <div class="text-blue-800 font-semibold">Premium Content</div>
                                <div class="text-blue-600 text-sm font-medium">$<?php echo e(number_format($book->price, 0)); ?> - One-time purchase</div>
                            </div>
                        <?php endif; ?>

                        <?php if($book->file_path): ?>
                            <a href="/books/<?php echo e($book->slug); ?>/download" class="block w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white text-center font-bold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-blue-800 transform hover:scale-105 transition-all duration-200">
                                <i class="fas fa-download mr-2"></i>
                                <?php if($book->is_free): ?>
                                    Download Free
                                <?php else: ?>
                                    Buy & Download
                                <?php endif; ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Share Section -->
            <div class="border-t border-gray-200 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Share This Book</h3>
                        <p class="text-gray-600 font-medium">Help others discover this valuable resource</p>
                    </div>

                    <div class="flex items-center space-x-3">
                        <a
                            href="https://twitter.com/intent/tweet?text=<?php echo e(urlencode($book->title . ' by ' . $book->author)); ?>&url=<?php echo e(urlencode(url()->current())); ?>"
                            target="_blank"
                            class="w-10 h-10 bg-blue-500 hover:bg-blue-600 text-white rounded-full flex items-center justify-center transition-colors duration-200"
                            aria-label="Share on Twitter"
                        >
                            <i class="fab fa-twitter text-sm"></i>
                        </a>
                        <a
                            href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(urlencode(url()->current())); ?>"
                            target="_blank"
                            class="w-10 h-10 bg-blue-600 hover:bg-blue-700 text-white rounded-full flex items-center justify-center transition-colors duration-200"
                            aria-label="Share on Facebook"
                        >
                            <i class="fab fa-facebook-f text-sm"></i>
                        </a>
                        <a
                            href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo e(urlencode(url()->current())); ?>"
                            target="_blank"
                            class="w-10 h-10 bg-blue-700 hover:bg-blue-800 text-white rounded-full flex items-center justify-center transition-colors duration-200"
                            aria-label="Share on LinkedIn"
                        >
                            <i class="fab fa-linkedin-in text-sm"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Books -->
    <?php if($relatedBooks->count() > 0): ?>
    <section class="py-20 bg-gray-50 mt-16">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    Related Books
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
                    Discover more books by <?php echo e($book->author); ?> and similar topics.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <?php $__currentLoopData = $relatedBooks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedBook): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="card card-hover group">
                        <!-- Book Cover -->
                        <div class="relative overflow-hidden">
                            <?php if($relatedBook->image): ?>
                                <img
                                    src="<?php echo e(Storage::url($relatedBook->image)); ?>"
                                    alt="<?php echo e($relatedBook->title); ?>"
                                    class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                >
                            <?php else: ?>
                                <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                    <i class="fas fa-book text-gray-400 text-3xl"></i>
                                </div>
                            <?php endif; ?>

                            <!-- Price Badge -->
                            <div class="absolute top-4 right-4">
                                <?php if($relatedBook->is_free): ?>
                                    <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold">
                                        Free
                                    </span>
                                <?php else: ?>
                                    <span class="bg-primary-600 text-white px-2 py-1 rounded text-xs font-bold">
                                        $<?php echo e(number_format($relatedBook->price, 0)); ?>

                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Book Content -->
                        <div class="p-4">
                            <h3 class="text-lg font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
                                <?php echo e(Str::limit($relatedBook->title, 40)); ?>

                            </h3>
                            <p class="text-gray-600 mb-3 font-medium text-sm">by <?php echo e($relatedBook->author); ?></p>
                            <a href="/books/<?php echo e($relatedBook->slug); ?>" class="block w-full btn btn-outline text-center font-semibold text-sm">
                                <i class="fas fa-eye mr-2"></i>
                                View Details
                            </a>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-primary mt-16">
        <div class="max-w-4xl mx-auto container-padding text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Want to Learn More?
            </h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed font-medium">
                Complement your reading with our interactive courses and personalized instruction.
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/courses" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                    <i class="fas fa-graduation-cap mr-3"></i>
                    Browse Courses
                </a>
                <a href="/trial-class" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 text-lg font-semibold px-8 py-4">
                    <i class="fas fa-play-circle mr-3"></i>
                    Try Free Class
                </a>
            </div>
        </div>
    </section>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $attributes = $__attributesOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__attributesOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $component = $__componentOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__componentOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\zajelwebsite\resources\views/books/show.blade.php ENDPATH**/ ?>