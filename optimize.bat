@echo off
echo Optimizing Laravel Application...
cd /d "d:\laragon\www\zajelwebsite"

echo.
echo [1/6] Clearing caches...
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

echo.
echo [2/6] Optimizing for production...
php artisan config:cache
php artisan route:cache
php artisan view:cache

echo.
echo [3/6] Creating storage link...
php artisan storage:link

echo.
echo [4/6] Building assets...
npm run build

echo.
echo [5/6] Optimizing composer autoloader...
composer dump-autoload --optimize

echo.
echo [6/6] Running migrations and seeders...
php artisan migrate:fresh --seed

echo.
echo ✅ Optimization complete!
echo.
echo Next steps:
echo 1. Create admin user: php artisan make:filament-user
echo 2. Start server: php artisan serve
echo 3. Visit: http://localhost:8000
echo.
pause
