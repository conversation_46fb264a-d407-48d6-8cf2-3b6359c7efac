<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TrialClassRequestResource\Pages;
use App\Filament\Resources\TrialClassRequestResource\RelationManagers;
use App\Models\TrialClassRequest;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TrialClassRequestResource extends Resource
{
    protected static ?string $model = TrialClassRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    protected static ?string $navigationLabel = 'Trial Class Requests';

    protected static ?string $modelLabel = 'Trial Class Request';

    protected static ?string $pluralModelLabel = 'Trial Class Requests';

    protected static ?int $navigationSort = 2;

    protected static ?string $navigationGroup = 'Communications';

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::unread()->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return static::getModel()::unread()->count() > 0 ? 'danger' : null;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('phone')
                    ->tel()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('age_group')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('course_interest')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('preferred_time')
                    ->maxLength(255),
                Forms\Components\Textarea::make('notes')
                    ->columnSpanFull(),
                Forms\Components\Toggle::make('is_read')
                    ->required(),
                Forms\Components\DateTimePicker::make('read_at'),
                Forms\Components\Select::make('status')
                    ->label('Request Status')
                    ->options([
                        'pending' => 'Pending',
                        'contacted' => 'Contacted',
                        'scheduled' => 'Scheduled',
                        'completed' => 'Completed',
                    ])
                    ->default('pending')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('age_group')
                    ->searchable(),
                Tables\Columns\TextColumn::make('course_interest')
                    ->searchable(),
                Tables\Columns\TextColumn::make('preferred_time')
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_read')
                    ->boolean(),
                Tables\Columns\TextColumn::make('read_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTrialClassRequests::route('/'),
            'create' => Pages\CreateTrialClassRequest::route('/create'),
            'edit' => Pages\EditTrialClassRequest::route('/{record}/edit'),
        ];
    }
}
