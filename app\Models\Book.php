<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Book extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'author',
        'image',
        'file_path',
        'file_size',
        'pages',
        'language',
        'isbn',
        'publication_date',
        'is_free',
        'price',
        'is_featured',
        'is_active',
        'download_count',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'sort_order'
    ];

    protected $casts = [
        'is_free' => 'boolean',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'price' => 'decimal:2',
        'publication_date' => 'date',
        'download_count' => 'integer',
        'pages' => 'integer',
    ];

    public function getRouteKeyName()
    {
        return 'slug';
    }

    public function incrementDownloadCount()
    {
        $this->increment('download_count');
    }
}
