<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PaymentSettingResource\Pages;
use App\Filament\Resources\PaymentSettingResource\RelationManagers;
use App\Models\PaymentSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PaymentSettingResource extends Resource
{
    protected static ?string $model = PaymentSetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    protected static ?string $navigationGroup = 'Settings';
    protected static ?string $navigationLabel = 'Payment Integrations';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Payment Provider')
                    ->schema([
                        Forms\Components\Select::make('provider')
                            ->required()
                            ->options([
                                'stripe' => 'Stripe',
                                'paypal' => 'PayPal',
                            ])
                            ->live()
                            ->afterStateUpdated(fn (Forms\Set $set, $state) => $set('additional_settings', [])),

                        Forms\Components\Toggle::make('is_enabled')
                            ->label('Enable Provider')
                            ->default(false),

                        Forms\Components\Toggle::make('is_sandbox')
                            ->label('Sandbox Mode')
                            ->default(true)
                            ->helperText('Enable for testing, disable for live payments'),
                    ])->columns(3),

                Forms\Components\Section::make('API Credentials')
                    ->schema([
                        // Stripe Fields
                        Forms\Components\TextInput::make('public_key')
                            ->label(fn (Forms\Get $get) => $get('provider') === 'stripe' ? 'Publishable Key' : 'Client ID')
                            ->required()
                            ->password()
                            ->revealable()
                            ->helperText(fn (Forms\Get $get) => $get('provider') === 'stripe'
                                ? 'Your Stripe publishable key (pk_test_... or pk_live_...)'
                                : 'Your PayPal Client ID'),

                        Forms\Components\TextInput::make('secret_key')
                            ->label(fn (Forms\Get $get) => $get('provider') === 'stripe' ? 'Secret Key' : 'Client Secret')
                            ->required()
                            ->password()
                            ->revealable()
                            ->helperText(fn (Forms\Get $get) => $get('provider') === 'stripe'
                                ? 'Your Stripe secret key (sk_test_... or sk_live_...)'
                                : 'Your PayPal Client Secret'),

                        Forms\Components\TextInput::make('webhook_secret')
                            ->label('Webhook Secret')
                            ->password()
                            ->revealable()
                            ->helperText(fn (Forms\Get $get) => $get('provider') === 'stripe'
                                ? 'Stripe webhook endpoint secret (whsec_...)'
                                : 'PayPal webhook ID (optional)'),
                    ])->columns(1),

                Forms\Components\Section::make('Additional Settings')
                    ->schema([
                        Forms\Components\KeyValue::make('additional_settings')
                            ->label('Extra Configuration')
                            ->helperText('Provider-specific settings'),

                        Forms\Components\Placeholder::make('connection_test')
                            ->label('Connection Test')
                            ->content(fn ($record) => $record ?
                                ($record->is_enabled ? '✅ Provider is enabled' : '⚠️ Provider is disabled') :
                                '💡 Save the settings first to test connection'
                            ),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('provider')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'stripe' => 'info',
                        'paypal' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('is_enabled')
                    ->boolean()
                    ->label('Enabled'),

                Tables\Columns\IconColumn::make('is_sandbox')
                    ->boolean()
                    ->label('Sandbox'),

                Tables\Columns\TextColumn::make('public_key')
                    ->label('Public Key')
                    ->limit(20)
                    ->tooltip(fn ($record) => $record->public_key ? 'Configured' : 'Not set'),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_enabled')
                    ->label('Enabled'),
                Tables\Filters\TernaryFilter::make('is_sandbox')
                    ->label('Sandbox Mode'),
            ])
            ->actions([
                Tables\Actions\Action::make('test_connection')
                    ->label('Test Connection')
                    ->icon('heroicon-o-wifi')
                    ->color('info')
                    ->action(function ($record) {
                        $paymentService = app(\App\Services\PaymentService::class);
                        $result = $paymentService->testConnection($record);

                        if ($result['success']) {
                            \Filament\Notifications\Notification::make()
                                ->title('Connection Successful')
                                ->body($result['message'])
                                ->success()
                                ->send();
                        } else {
                            \Filament\Notifications\Notification::make()
                                ->title('Connection Failed')
                                ->body($result['message'])
                                ->danger()
                                ->send();
                        }
                    })
                    ->visible(fn ($record) => $record && !empty($record->public_key) && !empty($record->secret_key)),

                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPaymentSettings::route('/'),
            'create' => Pages\CreatePaymentSetting::route('/create'),
            'edit' => Pages\EditPaymentSetting::route('/{record}/edit'),
        ];
    }
}
