<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PaymentSettingResource\Pages;
use App\Filament\Resources\PaymentSettingResource\RelationManagers;
use App\Models\PaymentSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PaymentSettingResource extends Resource
{
    protected static ?string $model = PaymentSetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    protected static ?string $navigationGroup = 'Settings';
    protected static ?string $navigationLabel = 'Payment Integrations';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Payment Provider')
                    ->schema([
                        Forms\Components\Select::make('provider')
                            ->required()
                            ->options([
                                'stripe' => 'Stripe',
                                'paypal' => 'PayPal',
                            ])
                            ->live()
                            ->afterStateUpdated(fn (Forms\Set $set, $state) => $set('additional_settings', [])),

                        Forms\Components\Toggle::make('is_enabled')
                            ->label('Enable Provider')
                            ->default(false),

                        Forms\Components\Toggle::make('is_sandbox')
                            ->label('Sandbox Mode')
                            ->default(true)
                            ->helperText('Enable for testing, disable for live payments'),
                    ])->columns(3),

                Forms\Components\Section::make('API Credentials')
                    ->schema([
                        Forms\Components\TextInput::make('public_key')
                            ->label('Public/Client Key')
                            ->required()
                            ->password()
                            ->revealable()
                            ->helperText('Your publishable key (safe to expose in frontend)'),

                        Forms\Components\TextInput::make('secret_key')
                            ->label('Secret Key')
                            ->required()
                            ->password()
                            ->revealable()
                            ->helperText('Your secret key (keep this secure)'),

                        Forms\Components\TextInput::make('webhook_secret')
                            ->label('Webhook Secret')
                            ->password()
                            ->revealable()
                            ->helperText('Optional: For webhook verification'),
                    ])->columns(1),

                Forms\Components\Section::make('Additional Settings')
                    ->schema([
                        Forms\Components\KeyValue::make('additional_settings')
                            ->label('Extra Configuration')
                            ->helperText('Provider-specific settings (JSON format)'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('provider')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'stripe' => 'info',
                        'paypal' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('is_enabled')
                    ->boolean()
                    ->label('Enabled'),

                Tables\Columns\IconColumn::make('is_sandbox')
                    ->boolean()
                    ->label('Sandbox'),

                Tables\Columns\TextColumn::make('public_key')
                    ->label('Public Key')
                    ->limit(20)
                    ->tooltip(fn ($record) => $record->public_key ? 'Configured' : 'Not set'),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_enabled')
                    ->label('Enabled'),
                Tables\Filters\TernaryFilter::make('is_sandbox')
                    ->label('Sandbox Mode'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPaymentSettings::route('/'),
            'create' => Pages\CreatePaymentSetting::route('/create'),
            'edit' => Pages\EditPaymentSetting::route('/{record}/edit'),
        ];
    }
}
