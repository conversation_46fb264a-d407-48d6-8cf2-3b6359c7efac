<?php

namespace App\Http\Controllers;

use App\Models\Book;
use App\Models\BookPurchase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class BookController extends Controller
{
    public function index(Request $request)
    {
        $query = Book::where('is_active', true);

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('author', 'like', "%{$search}%");
            });
        }

        // Filter by type (free/paid)
        if ($request->has('type') && $request->type) {
            if ($request->type === 'free') {
                $query->where('is_free', true);
            } elseif ($request->type === 'paid') {
                $query->where('is_free', false);
            }
        }

        // Filter by language
        if ($request->has('language') && $request->language) {
            $query->where('language', $request->language);
        }

        // Sort
        $sortBy = $request->get('sort', 'featured');
        switch ($sortBy) {
            case 'title':
                $query->orderBy('title', 'asc');
                break;
            case 'author':
                $query->orderBy('author', 'asc');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'popular':
                $query->orderBy('download_count', 'desc');
                break;
            case 'featured':
            default:
                $query->orderBy('is_featured', 'desc')->orderBy('sort_order');
                break;
        }

        $books = $query->paginate(12);

        // Get featured books
        $featuredBooks = Book::where('is_active', true)
            ->where('is_featured', true)
            ->orderBy('sort_order')
            ->take(3)
            ->get();

        // Get available languages
        $languages = Book::where('is_active', true)
            ->distinct()
            ->pluck('language')
            ->filter()
            ->sort()
            ->values();

        return view('books.index', compact('books', 'featuredBooks', 'languages'));
    }

    public function show(Book $book)
    {
        // Check if book is active
        if (!$book->is_active) {
            abort(404);
        }

        // Get related books
        $relatedBooks = Book::where('is_active', true)
            ->where('id', '!=', $book->id)
            ->where(function($query) use ($book) {
                $query->where('author', $book->author)
                      ->orWhere('language', $book->language);
            })
            ->take(4)
            ->get();

        return view('books.show', compact('book', 'relatedBooks'));
    }

    public function download(Book $book)
    {
        // Debug info
        Log::info('Download attempt', [
            'book_id' => $book->id,
            'book_slug' => $book->slug,
            'book_title' => $book->title,
            'is_active' => $book->is_active,
            'file_path' => $book->file_path,
            'is_free' => $book->is_free,
        ]);

        // Check if book is active and has a file
        if (!$book->is_active || !$book->file_path) {
            Log::warning('Book not active or no file', ['book_id' => $book->id]);
            abort(404, 'Book not available for download');
        }

        // Check if file exists
        if (!Storage::exists($book->file_path)) {
            Log::error('File not found', ['file_path' => $book->file_path]);
            abort(404, 'File not found');
        }

        // If book is not free, redirect to checkout
        if (!$book->is_free) {
            Log::info('Redirecting to checkout for paid book', ['book_id' => $book->id]);
            return redirect()->route('books.checkout', $book);
        }

        // Increment download count
        $book->incrementDownloadCount();

        // Get file info
        $fileName = $book->title . '.' . pathinfo($book->file_path, PATHINFO_EXTENSION);

        Log::info('Starting download', ['file_name' => $fileName]);

        return Storage::download($book->file_path, $fileName);
    }

    public function downloadPurchased($token)
    {
        $purchase = BookPurchase::where('download_token', $token)->first();

        if (!$purchase || !$purchase->canDownload()) {
            abort(404, 'Download not available or expired');
        }

        $book = $purchase->book;

        // Check if file exists
        if (!Storage::exists($book->file_path)) {
            abort(404, 'File not found');
        }

        // Increment download counts
        $purchase->incrementDownloadCount();
        $book->incrementDownloadCount();

        // Get file info
        $fileName = $book->title . '.' . pathinfo($book->file_path, PATHINFO_EXTENSION);

        return Storage::download($book->file_path, $fileName);
    }
}
