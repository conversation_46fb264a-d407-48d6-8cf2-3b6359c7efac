<x-layouts.app>
    <x-slot name="title">Frequently Asked Questions - Zajel Arabic Academy</x-slot>
    <x-slot name="metaDescription">Find answers to common questions about our Arabic and Quran courses, enrollment process, pricing, and learning methods at Zajel Arabic Academy.</x-slot>

    <!-- Page Header -->
    <section class="bg-gradient-primary text-white py-16 md:py-20">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                    Frequently Asked Questions
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed font-medium">
                    Find answers to common questions about our courses, enrollment, and learning process.
                </p>
            </div>
        </div>
    </section>

    <!-- FAQ Content -->
    <section class="section-padding bg-white">
        <div class="max-w-4xl mx-auto container-padding">
            <div class="space-y-8">
                @foreach($faqs as $index => $faq)
                    <div class="card p-6">
                        <button 
                            class="w-full text-left flex items-center justify-between focus:outline-none"
                            onclick="toggleFaq({{ $index }})"
                        >
                            <h3 class="text-lg md:text-xl font-bold text-gray-900 pr-4">
                                {{ $faq['question'] }}
                            </h3>
                            <i id="icon-{{ $index }}" class="fas fa-chevron-down text-primary-600 transition-transform duration-200"></i>
                        </button>
                        <div id="answer-{{ $index }}" class="hidden mt-4 pt-4 border-t border-gray-200">
                            <p class="text-gray-700 leading-relaxed font-medium">
                                {{ $faq['answer'] }}
                            </p>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Additional FAQ Categories -->
            <div class="mt-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">More Questions?</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- General Questions -->
                    <div class="text-center">
                        <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-question-circle text-primary-600 text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">General Questions</h3>
                        <p class="text-gray-600 mb-4 font-medium">
                            Questions about our academy, teaching methods, and general information.
                        </p>
                        <a href="/contact" class="text-primary-600 hover:text-primary-700 font-semibold">
                            Contact Us →
                        </a>
                    </div>

                    <!-- Technical Support -->
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-headset text-green-600 text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">Technical Support</h3>
                        <p class="text-gray-600 mb-4 font-medium">
                            Need help with platform access, video calls, or technical issues?
                        </p>
                        <a href="/contact" class="text-primary-600 hover:text-primary-700 font-semibold">
                            Get Help →
                        </a>
                    </div>

                    <!-- Course Specific -->
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-graduation-cap text-blue-600 text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">Course Questions</h3>
                        <p class="text-gray-600 mb-4 font-medium">
                            Specific questions about course content, curriculum, and learning outcomes.
                        </p>
                        <a href="/courses" class="text-primary-600 hover:text-primary-700 font-semibold">
                            Browse Courses →
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section-padding bg-gradient-primary">
        <div class="max-w-4xl mx-auto container-padding text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Still Have Questions?
            </h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed font-medium">
                Our team is here to help! Contact us directly or start with a free trial class to experience our teaching firsthand.
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/contact" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                    <i class="fas fa-envelope mr-3"></i>
                    Contact Us
                </a>
                <a href="/trial-class" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 text-lg font-semibold px-8 py-4">
                    <i class="fas fa-play-circle mr-3"></i>
                    Try Free Class
                </a>
            </div>
        </div>
    </section>

    <!-- FAQ JavaScript -->
    <script>
        function toggleFaq(index) {
            const answer = document.getElementById(`answer-${index}`);
            const icon = document.getElementById(`icon-${index}`);
            
            if (answer.classList.contains('hidden')) {
                answer.classList.remove('hidden');
                icon.classList.add('rotate-180');
            } else {
                answer.classList.add('hidden');
                icon.classList.remove('rotate-180');
            }
        }
    </script>
</x-layouts.app>
