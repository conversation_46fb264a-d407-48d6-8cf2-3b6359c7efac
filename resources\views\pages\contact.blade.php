<x-layouts.app>
    <x-slot name="title">Contact Us - Zajel Arabic Academy</x-slot>
    <x-slot name="metaDescription">Get in touch with Zajel Arabic Academy. Contact our team for questions about courses, enrollment, or any assistance you need with your Arabic and Quran learning journey.</x-slot>

    <!-- Page Header -->
    <section class="bg-gradient-to-br from-blue-600 to-blue-800 text-white py-20">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                    Contact Us
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed font-medium">
                    We're here to help you on your Arabic and Quran learning journey. Reach out to us anytime!
                </p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
                <!-- Contact Form -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                    <div class="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-6">
                        <i class="fas fa-envelope mr-2"></i>
                        Send us a message
                    </div>

                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                        Get in Touch
                    </h2>

                    <p class="text-lg text-gray-700 mb-8 leading-relaxed font-medium">
                        Have questions about our courses or need assistance? Fill out the form below and we'll get back to you as soon as possible.
                    </p>

                    @if(session('success'))
                        <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle mr-2"></i>
                                {{ session('success') }}
                            </div>
                        </div>
                    @endif

                    <form id="contact-form" method="POST" action="/contact" class="space-y-6">
                        @csrf

                        <!-- Honeypot field - hidden from users -->
                        <input type="text" name="website" style="display: none;" tabindex="-1" autocomplete="off">

                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-semibold text-gray-900 mb-2">
                                Full Name *
                            </label>
                            <input
                                type="text"
                                id="name"
                                name="name"
                                value="{{ old('name') }}"
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium @error('name') border-red-500 @enderror"
                                placeholder="Enter your full name"
                            >
                            @error('name')
                                <p class="mt-1 text-sm text-red-600 font-medium">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-semibold text-gray-900 mb-2">
                                Email Address *
                            </label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                value="{{ old('email') }}"
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium @error('email') border-red-500 @enderror"
                                placeholder="Enter your email address"
                            >
                            @error('email')
                                <p class="mt-1 text-sm text-red-600 font-medium">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Subject -->
                        <div>
                            <label for="subject" class="block text-sm font-semibold text-gray-900 mb-2">
                                Subject *
                            </label>
                            <select
                                id="subject"
                                name="subject"
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium @error('subject') border-red-500 @enderror"
                            >
                                <option value="">Select a subject</option>
                                <option value="Course Inquiry" {{ old('subject') === 'Course Inquiry' ? 'selected' : '' }}>Course Inquiry</option>
                                <option value="Enrollment" {{ old('subject') === 'Enrollment' ? 'selected' : '' }}>Enrollment</option>
                                <option value="Technical Support" {{ old('subject') === 'Technical Support' ? 'selected' : '' }}>Technical Support</option>
                                <option value="Billing" {{ old('subject') === 'Billing' ? 'selected' : '' }}>Billing</option>
                                <option value="General Question" {{ old('subject') === 'General Question' ? 'selected' : '' }}>General Question</option>
                                <option value="Other" {{ old('subject') === 'Other' ? 'selected' : '' }}>Other</option>
                            </select>
                            @error('subject')
                                <p class="mt-1 text-sm text-red-600 font-medium">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Message -->
                        <div>
                            <label for="message" class="block text-sm font-semibold text-gray-900 mb-2">
                                Message *
                            </label>
                            <textarea
                                id="message"
                                name="message"
                                rows="6"
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium @error('message') border-red-500 @enderror"
                                placeholder="Tell us how we can help you..."
                            >{{ old('message') }}</textarea>
                            @error('message')
                                <p class="mt-1 text-sm text-red-600 font-medium">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-lg font-semibold py-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl">
                            <i class="fas fa-paper-plane mr-2"></i>
                            Send Message
                        </button>
                    </form>
                </div>

                <!-- Contact Information -->
                <div class="bg-gray-50 rounded-2xl p-8">
                    <div class="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-6">
                        <i class="fas fa-info-circle mr-2"></i>
                        Contact Information
                    </div>

                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                        Let's Connect
                    </h2>

                    <p class="text-lg text-gray-700 mb-8 leading-relaxed font-medium">
                        We're always happy to hear from our students and prospective learners. Here are the best ways to reach us.
                    </p>

                    <!-- Contact Details -->
                    <div class="space-y-6 mb-8">
                        @if($contactEmail)
                            <div class="flex items-start space-x-4 bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                                <div class="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-envelope text-blue-600 text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-1">Email Us</h3>
                                    <p class="text-gray-600 font-medium mb-2">Send us an email anytime</p>
                                    <a href="mailto:{{ $contactEmail }}" class="text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-200">
                                        {{ $contactEmail }}
                                    </a>
                                </div>
                            </div>
                        @endif

                        @if($contactPhone)
                            <div class="flex items-start space-x-4 bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                                <div class="flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-phone text-green-600 text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-1">Call Us</h3>
                                    <p class="text-gray-600 font-medium mb-2">Available during business hours</p>
                                    <a href="tel:{{ $contactPhone }}" class="text-green-600 hover:text-green-700 font-semibold transition-colors duration-200">
                                        {{ $contactPhone }}
                                    </a>
                                </div>
                            </div>
                        @endif

                        @if($contactAddress)
                            <div class="flex items-start space-x-4 bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                                <div class="flex-shrink-0 w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-map-marker-alt text-purple-600 text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-1">Visit Us</h3>
                                    <p class="text-gray-600 font-medium mb-2">Our office location</p>
                                    <p class="text-gray-700 font-medium leading-relaxed">
                                        {{ $contactAddress }}
                                    </p>
                                </div>
                            </div>
                        @endif

                        <!-- Response Time -->
                        <div class="flex items-start space-x-4 bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                            <div class="flex-shrink-0 w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-clock text-orange-600 text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">Response Time</h3>
                                <p class="text-gray-600 font-medium mb-2">We typically respond within</p>
                                <p class="text-gray-700 font-medium">24 hours on business days</p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <a href="/trial-class" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium">
                                <i class="fas fa-play-circle mr-3 text-blue-600"></i>
                                Book a Free Trial Class
                            </a>
                            <a href="/courses" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium">
                                <i class="fas fa-graduation-cap mr-3 text-blue-600"></i>
                                Browse Our Courses
                            </a>
                            <a href="/faq" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium">
                                <i class="fas fa-question-circle mr-3 text-blue-600"></i>
                                Check FAQ
                            </a>
                            <a href="/pricing" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium">
                                <i class="fas fa-tags mr-3 text-blue-600"></i>
                                View Pricing
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-4 max-w-4xl">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    Frequently Asked Questions
                </h2>
                <p class="text-xl text-gray-600 leading-relaxed font-medium">
                    Find quick answers to common questions before reaching out.
                </p>
            </div>

            <div class="space-y-6">
                <!-- FAQ Item 1 -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-shadow duration-300">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">How do I enroll in a course?</h3>
                    <p class="text-gray-700 leading-relaxed font-medium">
                        You can enroll by browsing our courses, selecting the one that interests you, and clicking "Enroll Now." You can also start with a free trial class to experience our teaching style.
                    </p>
                </div>

                <!-- FAQ Item 2 -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-shadow duration-300">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">What are your class timings?</h3>
                    <p class="text-gray-700 leading-relaxed font-medium">
                        We offer flexible scheduling to accommodate students from different time zones. Classes are available throughout the day, and you can choose times that work best for your schedule.
                    </p>
                </div>

                <!-- FAQ Item 3 -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-shadow duration-300">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Do you offer certificates?</h3>
                    <p class="text-gray-700 leading-relaxed font-medium">
                        Yes, we provide certificates of completion for all our courses. These certificates are recognized and can be used for academic or professional purposes.
                    </p>
                </div>

                <!-- FAQ Item 4 -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-shadow duration-300">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">What if I need to cancel or reschedule a class?</h3>
                    <p class="text-gray-700 leading-relaxed font-medium">
                        We understand that schedules can change. You can reschedule classes up to 24 hours in advance through your student portal or by contacting our support team.
                    </p>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="/faq" class="inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-lg">
                    <i class="fas fa-question-circle mr-2"></i>
                    View All FAQs
                </a>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-br from-blue-600 to-blue-800">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Start Learning?
            </h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed font-medium max-w-2xl mx-auto">
                Don't wait! Begin your Arabic and Quran learning journey today with a free trial class.
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/trial-class" class="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200 shadow-lg">
                    <i class="fas fa-play-circle mr-3"></i>
                    Book Free Trial
                </a>
                <a href="/courses" class="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors duration-200">
                    <i class="fas fa-graduation-cap mr-3"></i>
                    Browse Courses
                </a>
            </div>
        </div>
    </section>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const contactForm = document.getElementById('contact-form');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;

                    // Show loading state
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';

                    // Clear previous errors
                    clearFormErrors(this);

                    fetch('/contact', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showSuccessMessage(data.message);
                            this.reset();
                        } else {
                            if (data.errors) {
                                showFormErrors(this, data.errors);
                            } else {
                                showErrorMessage(data.message || 'Something went wrong. Please try again.');
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showErrorMessage('Something went wrong. Please try again.');
                    })
                    .finally(() => {
                        // Reset button state
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                    });
                });
            }
        });

        // Helper Functions
        function showSuccessMessage(message) {
            const alert = createAlert('success', message);
            document.body.appendChild(alert);

            // Auto remove after 5 seconds
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        function showErrorMessage(message) {
            const alert = createAlert('error', message);
            document.body.appendChild(alert);

            // Auto remove after 5 seconds
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        function createAlert(type, message) {
            const alert = document.createElement('div');
            alert.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-md transition-all duration-300 ${
                type === 'success'
                    ? 'bg-green-100 border border-green-400 text-green-700'
                    : 'bg-red-100 border border-red-400 text-red-700'
            }`;

            alert.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-3 text-lg"></i>
                    <span class="flex-1">${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-500 hover:text-gray-700 focus:outline-none">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            return alert;
        }

        function showFormErrors(form, errors) {
            Object.keys(errors).forEach(field => {
                const input = form.querySelector(`[name="${field}"]`);
                if (input) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'text-red-600 text-sm mt-1 form-error';
                    errorDiv.textContent = errors[field][0];

                    input.classList.add('border-red-500');
                    input.parentNode.appendChild(errorDiv);
                }
            });
        }

        function clearFormErrors(form) {
            // Remove error classes
            form.querySelectorAll('.border-red-500').forEach(input => {
                input.classList.remove('border-red-500');
            });

            // Remove error messages
            form.querySelectorAll('.form-error').forEach(error => {
                error.remove();
            });
        }
    </script>
    @endpush
</x-layouts.app>
