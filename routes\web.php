<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\BookController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\PricingController;
use App\Http\Controllers\Admin\PricingPlanController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\TrialClassController;
use App\Http\Controllers\TeacherController;
use App\Http\Controllers\PaymentController;

// Home page
Route::get('/', [HomeController::class, 'index'])->name('home');

// Courses
Route::get('/courses', [CourseController::class, 'index'])->name('courses.index');
Route::get('/courses/{course}', [CourseController::class, 'show'])->name('courses.show');
Route::post('/courses/{course}/reviews', [CourseController::class, 'storeReview'])->name('courses.reviews.store');

// Blog
Route::get('/blog', [BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/{post}', [BlogController::class, 'show'])->name('blog.show');

// Books
Route::get('/books', [BookController::class, 'index'])->name('books.index');
Route::get('/books/{book}', [BookController::class, 'show'])->name('books.show');
Route::get('/books/{book}/download', [BookController::class, 'download'])->name('books.download');

// Book Payments
Route::get('/books/{book}/checkout', [PaymentController::class, 'showCheckout'])->name('books.checkout');
Route::post('/books/{book}/payment', [PaymentController::class, 'processPayment'])->name('books.payment');
Route::get('/purchase/success/{token}', [PaymentController::class, 'success'])->name('books.purchase.success');
Route::get('/download/{token}', [BookController::class, 'downloadPurchased'])->name('books.download.purchased');

// Payment Webhooks
Route::post('/webhooks/{provider}', [PaymentController::class, 'webhook'])->name('payment.webhook');

// Teachers
Route::get('/teachers', [TeacherController::class, 'index'])->name('teachers.index');
Route::get('/teachers/{teacher}', [TeacherController::class, 'show'])->name('teachers.show');

// Static Pages
Route::get('/about', [PageController::class, 'about'])->name('about');
Route::get('/contact', [PageController::class, 'contact'])->name('contact');
Route::get('/pricing', [PricingController::class, 'index'])->name('pricing');
Route::get('/trial-class', [PageController::class, 'trialClass'])->name('trial-class');
Route::get('/faq', [PageController::class, 'faq'])->name('faq');

// Form Submissions with protection
Route::middleware(['throttle.forms'])->group(function () {
    Route::post('/contact', [ContactController::class, 'store'])->name('contact.submit');
    Route::post('/trial-class', [TrialClassController::class, 'store'])->name('trial-class.submit');
});

// Legal Pages
Route::get('/privacy-policy', [PageController::class, 'privacyPolicy'])->name('privacy-policy');
Route::get('/terms-of-service', [PageController::class, 'termsOfService'])->name('terms-of-service');
Route::get('/cookie-policy', [PageController::class, 'cookiePolicy'])->name('cookie-policy');

// Admin Routes (you can add authentication middleware later)
Route::prefix('admin')->name('admin.')->group(function () {
    Route::resource('pricing-plans', PricingPlanController::class);
});
