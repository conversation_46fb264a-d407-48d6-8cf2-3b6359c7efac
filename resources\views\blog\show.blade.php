<x-layouts.app>
    <x-slot name="title">{{ $post->meta_title ?: $post->title }} - Zajel Arabic Academy</x-slot>
    <x-slot name="metaDescription">{{ $post->meta_description ?: Str::limit($post->excerpt ?: strip_tags($post->content), 160) }}</x-slot>

    <!-- Article Header -->
    <article class="max-w-4xl mx-auto container-padding py-16 md:py-20">
        <!-- Breadcrumb -->
        <nav class="flex items-center space-x-2 text-gray-500 mb-8">
            <a href="/" class="hover:text-primary-600 transition-colors font-medium">Home</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <a href="/blog" class="hover:text-primary-600 transition-colors font-medium">Blog</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span class="text-gray-900 font-medium">{{ Str::limit($post->title, 50) }}</span>
        </nav>

        <!-- Article Meta -->
        <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
            <time datetime="{{ $post->published_at->format('Y-m-d') }}" class="font-medium">
                {{ $post->published_at->format('F d, Y') }}
            </time>
            <span>•</span>
            <span class="font-medium">{{ $post->reading_time }} min read</span>
            @if($post->views_count > 0)
                <span>•</span>
                <span class="font-medium">{{ number_format($post->views_count) }} views</span>
            @endif
        </div>

        <!-- Article Title -->
        <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-8 leading-tight">
            {{ $post->title }}
        </h1>

        <!-- Article Excerpt -->
        @if($post->excerpt)
            <div class="text-xl md:text-2xl text-gray-600 mb-8 leading-relaxed font-medium">
                {{ $post->excerpt }}
            </div>
        @endif

        <!-- Author Info -->
        <div class="flex items-center justify-between border-t border-b border-gray-200 py-6 mb-8">
            <div class="flex items-center">
                @if($post->author_image)
                    <img 
                        src="{{ Storage::url($post->author_image) }}" 
                        alt="{{ $post->author_name }}"
                        class="w-16 h-16 rounded-full object-cover mr-4"
                    >
                @else
                    <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-user text-primary-600 text-xl"></i>
                    </div>
                @endif
                <div>
                    <div class="text-lg font-semibold text-gray-900">{{ $post->author_name ?: 'Admin' }}</div>
                    @if($post->author_bio)
                        <div class="text-gray-600 font-medium">{{ $post->author_bio }}</div>
                    @else
                        <div class="text-gray-600 font-medium">Content Writer at Zajel Arabic Academy</div>
                    @endif
                </div>
            </div>

            <!-- Social Share -->
            <div class="flex items-center space-x-3">
                <span class="text-sm text-gray-600 font-medium">Share:</span>
                <a 
                    href="https://twitter.com/intent/tweet?text={{ urlencode($post->title) }}&url={{ urlencode(url()->current()) }}" 
                    target="_blank"
                    class="w-10 h-10 bg-blue-500 hover:bg-blue-600 text-white rounded-full flex items-center justify-center transition-colors duration-200"
                    aria-label="Share on Twitter"
                >
                    <i class="fab fa-twitter text-sm"></i>
                </a>
                <a 
                    href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(url()->current()) }}" 
                    target="_blank"
                    class="w-10 h-10 bg-blue-600 hover:bg-blue-700 text-white rounded-full flex items-center justify-center transition-colors duration-200"
                    aria-label="Share on Facebook"
                >
                    <i class="fab fa-facebook-f text-sm"></i>
                </a>
                <a 
                    href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(url()->current()) }}" 
                    target="_blank"
                    class="w-10 h-10 bg-blue-700 hover:bg-blue-800 text-white rounded-full flex items-center justify-center transition-colors duration-200"
                    aria-label="Share on LinkedIn"
                >
                    <i class="fab fa-linkedin-in text-sm"></i>
                </a>
            </div>
        </div>

        <!-- Featured Image -->
        @if($post->image)
            <div class="mb-12">
                <img 
                    src="{{ Storage::url($post->image) }}" 
                    alt="{{ $post->title }}"
                    class="w-full h-64 md:h-80 lg:h-96 object-cover rounded-2xl shadow-large"
                >
            </div>
        @endif

        <!-- Article Content -->
        <div class="prose prose-lg prose-primary max-w-none mb-12">
            <div class="text-gray-800 leading-relaxed font-medium text-lg">
                {!! nl2br(e($post->content)) !!}
            </div>
        </div>

        <!-- Tags -->
        @if($post->tags && count($post->tags) > 0)
            <div class="border-t border-gray-200 pt-8 mb-12">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
                <div class="flex flex-wrap gap-3">
                    @foreach($post->tags as $tag)
                        <a 
                            href="/blog?tag={{ $tag }}" 
                            class="px-4 py-2 bg-gray-100 hover:bg-primary-100 text-gray-700 hover:text-primary-700 font-medium rounded-lg transition-colors duration-200"
                        >
                            #{{ $tag }}
                        </a>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Article Footer -->
        <div class="border-t border-gray-200 pt-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
                <!-- Author Bio -->
                <div class="flex items-center">
                    @if($post->author_image)
                        <img 
                            src="{{ Storage::url($post->author_image) }}" 
                            alt="{{ $post->author_name }}"
                            class="w-12 h-12 rounded-full object-cover mr-4"
                        >
                    @else
                        <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user text-primary-600"></i>
                        </div>
                    @endif
                    <div>
                        <div class="font-semibold text-gray-900">Written by {{ $post->author_name ?: 'Admin' }}</div>
                        <div class="text-sm text-gray-600 font-medium">Published on {{ $post->published_at->format('F d, Y') }}</div>
                    </div>
                </div>

                <!-- Share Again -->
                <div class="flex items-center space-x-3">
                    <span class="text-sm text-gray-600 font-medium">Share this article:</span>
                    <a 
                        href="https://twitter.com/intent/tweet?text={{ urlencode($post->title) }}&url={{ urlencode(url()->current()) }}" 
                        target="_blank"
                        class="w-8 h-8 bg-gray-200 hover:bg-blue-500 hover:text-white text-gray-600 rounded-full flex items-center justify-center transition-all duration-200"
                    >
                        <i class="fab fa-twitter text-xs"></i>
                    </a>
                    <a 
                        href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(url()->current()) }}" 
                        target="_blank"
                        class="w-8 h-8 bg-gray-200 hover:bg-blue-600 hover:text-white text-gray-600 rounded-full flex items-center justify-center transition-all duration-200"
                    >
                        <i class="fab fa-facebook-f text-xs"></i>
                    </a>
                    <a 
                        href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(url()->current()) }}" 
                        target="_blank"
                        class="w-8 h-8 bg-gray-200 hover:bg-blue-700 hover:text-white text-gray-600 rounded-full flex items-center justify-center transition-all duration-200"
                    >
                        <i class="fab fa-linkedin-in text-xs"></i>
                    </a>
                </div>
            </div>
        </div>
    </article>

    <!-- Related Articles -->
    @if($relatedPosts->count() > 0)
    <section class="section-padding bg-gray-50">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    Related Articles
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
                    Continue reading with these related articles that might interest you.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($relatedPosts as $relatedPost)
                    <article class="card card-hover group">
                        <!-- Post Image -->
                        <div class="relative overflow-hidden">
                            @if($relatedPost->image)
                                <img 
                                    src="{{ Storage::url($relatedPost->image) }}" 
                                    alt="{{ $relatedPost->title }}"
                                    class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                >
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                    <i class="fas fa-newspaper text-gray-400 text-3xl"></i>
                                </div>
                            @endif
                        </div>

                        <!-- Post Content -->
                        <div class="p-6">
                            <!-- Post Meta -->
                            <div class="flex items-center text-sm text-gray-500 mb-3">
                                <time datetime="{{ $relatedPost->published_at->format('Y-m-d') }}" class="font-medium">
                                    {{ $relatedPost->published_at->format('M d, Y') }}
                                </time>
                                <span class="mx-2">•</span>
                                <span class="font-medium">{{ $relatedPost->reading_time }} min read</span>
                            </div>

                            <!-- Post Title -->
                            <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-200">
                                {{ $relatedPost->title }}
                            </h3>

                            <!-- Post Excerpt -->
                            <p class="text-gray-600 mb-4 leading-relaxed font-medium">
                                {{ Str::limit($relatedPost->excerpt ?: strip_tags($relatedPost->content), 120) }}
                            </p>

                            <!-- Read More Link -->
                            <a href="/blog/{{ $relatedPost->slug }}" class="inline-flex items-center text-primary-600 hover:text-primary-700 font-semibold transition-colors duration-200">
                                Read Article
                                <i class="fas fa-arrow-right ml-2 text-sm"></i>
                            </a>
                        </div>
                    </article>
                @endforeach
            </div>

            <!-- View All Articles -->
            <div class="text-center mt-12">
                <a href="/blog" class="btn btn-outline text-lg font-semibold px-8 py-4">
                    <i class="fas fa-newspaper mr-2"></i>
                    View All Articles
                </a>
            </div>
        </div>
    </section>
    @endif

    <!-- Newsletter CTA -->
    <section class="section-padding bg-gradient-primary">
        <div class="max-w-4xl mx-auto container-padding text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Stay Updated with Our Latest Articles
            </h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed font-medium">
                Subscribe to our newsletter and never miss our latest insights on Arabic learning and Islamic education.
            </p>
            
            <form class="max-w-md mx-auto flex flex-col sm:flex-row gap-4">
                <input 
                    type="email" 
                    placeholder="Enter your email address" 
                    class="flex-1 px-6 py-4 rounded-lg text-gray-900 font-medium focus:ring-2 focus:ring-white focus:outline-none"
                    required
                >
                <button type="submit" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                    <i class="fas fa-envelope mr-2"></i>
                    Subscribe
                </button>
            </form>
        </div>
    </section>

    <!-- Back to Top Enhancement -->
    <script>
        // Enhanced smooth scrolling for article reading
        document.addEventListener('DOMContentLoaded', function() {
            // Add reading progress indicator
            const article = document.querySelector('article');
            const progressBar = document.createElement('div');
            progressBar.className = 'fixed top-0 left-0 w-0 h-1 bg-primary-600 z-50 transition-all duration-150';
            document.body.appendChild(progressBar);

            window.addEventListener('scroll', function() {
                const articleHeight = article.offsetHeight;
                const windowHeight = window.innerHeight;
                const scrollTop = window.pageYOffset;
                const scrollPercent = (scrollTop / (articleHeight - windowHeight)) * 100;
                
                progressBar.style.width = Math.min(Math.max(scrollPercent, 0), 100) + '%';
            });
        });
    </script>
</x-layouts.app>
