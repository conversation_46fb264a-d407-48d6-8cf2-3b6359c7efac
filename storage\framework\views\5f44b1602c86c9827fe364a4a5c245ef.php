<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" dir="ltr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e($title ?? 'Zajel Arabic Academy - Learn Arabic & Quran Online'); ?></title>
    <meta name="description" content="<?php echo e($metaDescription ?? 'Learn Arabic language and Quran with expert instructors at Zajel Arabic Academy. Interactive online classes for all levels.'); ?>">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700|roboto:400,500,700|noto-sans-arabic:400,500,600,700|cairo:400,500,600,700" rel="stylesheet" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        accent: {
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'Roboto', 'Noto Sans Arabic', 'Cairo', 'sans-serif'],
                    },
                    backgroundImage: {
                        'gradient-primary': 'linear-gradient(135deg, #1e40af 0%, #2563eb 100%)',
                    }
                }
            }
        }
    </script>

    <!-- Vite Assets (JS only) -->
    <?php if(file_exists(public_path('build/manifest.json'))): ?>
        <?php echo app('Illuminate\Foundation\Vite')(['resources/js/app.js']); ?>
    <?php endif; ?>

    <!-- Custom CSS -->
    <style>
        /* Base Styles */
        body {
            font-family: 'Inter', 'Roboto', 'Noto Sans Arabic', 'Cairo', sans-serif;
            line-height: 1.6;
        }

        /* Utility Classes */
        .container-padding { @apply px-4 sm:px-6 lg:px-8; }
        .section-padding { @apply py-16 md:py-20 lg:py-24; }
        .card { @apply bg-white rounded-xl shadow-sm border border-gray-200; }

        /* Navigation Active States */
        header .nav-link {
            position: relative !important;
            color: #374151 !important;
            text-decoration: none !important;
            padding: 0.5rem 0 !important;
            transition: all 0.3s ease !important;
            display: inline-block !important;
        }

        header .nav-link:hover {
            color: #3b82f6 !important;
        }

        header .nav-link.active {
            color: #3b82f6 !important;
            font-weight: 600 !important;
        }

        header .nav-link.active::after {
            content: '' !important;
            position: absolute !important;
            bottom: -8px !important;
            left: 0 !important;
            right: 0 !important;
            height: 3px !important;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8) !important;
            border-radius: 2px !important;
        }
        .card-hover { @apply hover:shadow-lg transition-shadow duration-300; }
        .shadow-large { @apply shadow-2xl; }

        .btn { @apply inline-flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2; }
        .btn-primary { @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500; }
        .btn-outline { @apply border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white focus:ring-blue-500; }

        .nav-link { @apply text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 px-3 py-2 rounded-md; }
        .nav-link.active { @apply text-blue-600 bg-blue-50; }

        .mobile-nav-link { @apply flex items-center text-gray-700 hover:text-blue-600 hover:bg-blue-50 font-medium transition-all duration-200 px-4 py-3 rounded-lg; }
        .mobile-nav-link.active { @apply text-blue-600 bg-blue-50; }

        .footer-link { @apply text-gray-400 hover:text-white transition-colors duration-200 font-medium; }

        .bg-gradient-primary { background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%); }

        /* Typography improvements */
        body {
            font-feature-settings: "kern" 1, "liga" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        h1, h2, h3, h4, h5, h6 {
            letter-spacing: -0.025em;
            line-height: 1.2;
        }

        p, li {
            line-height: 1.7;
            letter-spacing: 0.01em;
        }

        /* Mobile Menu Animations */
        #mobile-menu {
            transition: transform 0.3s ease-in-out;
        }

        #mobile-overlay {
            transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
        }

        /* Smooth dropdown animations */
        .rotate-180 {
            transform: rotate(180deg);
        }

        /* Blob animations */
        @keyframes blob {
            0% {
                transform: translate(0px, 0px) scale(1);
            }
            33% {
                transform: translate(30px, -50px) scale(1.1);
            }
            66% {
                transform: translate(-20px, 20px) scale(0.9);
            }
            100% {
                transform: translate(0px, 0px) scale(1);
            }
        }

        .animate-blob {
            animation: blob 7s infinite;
        }

        .animation-delay-2000 {
            animation-delay: 2s;
        }

        .animation-delay-4000 {
            animation-delay: 4s;
        }
    </style>

    <!-- Additional Head Content -->
    <?php echo $__env->yieldPushContent('head'); ?>
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Mobile Top Bar (Trial Class) -->
    <div class="lg:hidden bg-gradient-to-r from-green-500 to-green-600 text-white py-2 px-4 text-center sticky top-0 z-50">
        <a href="/trial-class" class="inline-flex items-center text-sm font-semibold hover:text-green-100 transition-colors duration-200">
            <i class="fas fa-play-circle mr-2"></i>
            Book Your Free Trial Class Now - Limited Time Offer!
            <i class="fas fa-arrow-right ml-2"></i>
        </a>
    </div>

    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="flex items-center justify-between h-16 md:h-20">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-graduation-cap text-white text-xl"></i>
                        </div>
                        <div class="hidden sm:block">
                            <h1 class="text-xl font-bold text-gray-900">Zajel Arabic Academy</h1>
                            <p class="text-sm text-gray-600 font-medium">Learn Arabic & Quran</p>
                        </div>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex items-center space-x-8">
                    <a href="/" class="nav-link <?php echo e(request()->is('/') ? 'active' : ''); ?>">Home</a>

                    <!-- Courses Dropdown -->
                    <div class="relative group">
                        <button class="nav-link flex items-center <?php echo e(request()->is('courses*') ? 'active' : ''); ?>">
                            Courses
                            <i class="fas fa-chevron-down ml-1 text-xs transition-transform duration-200 group-hover:rotate-180"></i>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-72 bg-white rounded-lg shadow-xl border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                            <div class="p-4">
                                <div class="space-y-2">
                                    <?php
                                        $courses = \App\Models\Course::where('is_active', true)->take(5)->get();
                                    ?>

                                    <?php $__empty_1 = true; $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <a href="<?php echo e(route('courses.show', $course->slug)); ?>" class="flex items-center p-3 rounded-lg hover:bg-blue-50 transition-colors duration-200">
                                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-book text-blue-600"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-900"><?php echo e($course->title); ?></h4>
                                                <p class="text-sm text-gray-500"><?php echo e(Str::limit($course->description, 50)); ?></p>
                                            </div>
                                        </a>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <a href="/courses" class="flex items-center p-3 rounded-lg hover:bg-blue-50 transition-colors duration-200">
                                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-book text-blue-600"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-900">Arabic Language</h4>
                                                <p class="text-sm text-gray-500">Learn Arabic from basics to advanced</p>
                                            </div>
                                        </a>
                                        <a href="/courses" class="flex items-center p-3 rounded-lg hover:bg-blue-50 transition-colors duration-200">
                                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-book-quran text-green-600"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-900">Quran Memorization</h4>
                                                <p class="text-sm text-gray-500">Memorize Quran with Tajweed</p>
                                            </div>
                                        </a>
                                    <?php endif; ?>
                                </div>
                                <div class="border-t border-gray-200 mt-4 pt-4">
                                    <a href="/trial-class" class="flex items-center justify-center w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors duration-200">
                                        <i class="fas fa-play-circle mr-2"></i>
                                        Book Free Trial
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/teachers" class="nav-link <?php echo e(request()->is('teachers*') ? 'active' : ''); ?>">Teachers</a>
                    <a href="/pricing" class="nav-link <?php echo e(request()->is('pricing') ? 'active' : ''); ?>">Pricing</a>
                    <a href="/blog" class="nav-link <?php echo e(request()->is('blog*') ? 'active' : ''); ?>">Blog</a>
                    <a href="/books" class="nav-link <?php echo e(request()->is('books*') ? 'active' : ''); ?>">Books</a>
                    <a href="/about" class="nav-link <?php echo e(request()->is('about') ? 'active' : ''); ?>">About</a>
                    <a href="/contact" class="nav-link <?php echo e(request()->is('contact') ? 'active' : ''); ?>">Contact</a>
                </nav>

                <!-- CTA Buttons -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="/trial-class" class="btn <?php echo e(request()->is('trial-class') ? 'btn-primary' : 'btn-outline'); ?> transition-all duration-200 hover:scale-105">
                        <i class="fas fa-play-circle mr-2"></i>
                        Free Trial
                    </a>
                    <a href="/pricing" class="btn <?php echo e(request()->is('pricing') ? 'btn-primary ring-2 ring-blue-300' : 'btn-primary'); ?> transition-all duration-200 hover:scale-105">
                        <i class="fas fa-rocket mr-2"></i>
                        Get Started
                    </a>
                </div>

                <!-- Mobile Actions -->
                <div class="lg:hidden flex items-center space-x-3">
                    <!-- Mobile Trial Class Button -->
                    <a href="/trial-class" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white text-sm font-semibold rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
                        <i class="fas fa-play-circle mr-2"></i>
                        Trial
                    </a>

                    <!-- Mobile Menu Button -->
                    <button
                        type="button"
                        onclick="toggleMobileMenu()"
                        class="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors duration-200"
                        aria-expanded="false"
                        aria-label="Toggle main menu"
                    >
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 opacity-0 invisible transition-all duration-300 lg:hidden"></div>

    <!-- Mobile Navigation Menu -->
    <div id="mobile-menu" class="lg:hidden fixed inset-y-0 left-0 w-80 bg-white shadow-2xl transform -translate-x-full transition-transform duration-300 ease-in-out z-50 overflow-y-auto">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-700 p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <img src="<?php echo e(asset('images/logo.svg')); ?>" alt="Zajel Academy" class="h-8 w-auto">
                    <span class="text-lg font-bold text-white">Zajel Academy</span>
                </div>
                <button
                    type="button"
                    onclick="closeMobileMenu()"
                    class="inline-flex items-center justify-center p-2 rounded-md text-white hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-white transition-colors duration-200"
                    aria-label="Close menu"
                >
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- All Departments Header -->
        <div class="bg-yellow-400 px-4 py-3">
            <h3 class="text-gray-900 font-semibold text-lg">All Departments</h3>
        </div>

        <!-- Navigation Links -->
        <div class="py-2">
            <a href="/" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 border-b border-gray-100 transition-colors duration-200 <?php echo e(request()->is('/') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                <i class="fas fa-home mr-3 text-blue-500"></i>
                <span class="font-medium">Home</span>
            </a>

            <!-- Courses with Dropdown -->
            <div class="border-b border-gray-100">
                <button onclick="toggleMobileCourses()" class="flex items-center justify-between w-full px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors duration-200 <?php echo e(request()->is('courses*') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                    <div class="flex items-center">
                        <i class="fas fa-graduation-cap mr-3 text-blue-500"></i>
                        <span class="font-medium">Courses</span>
                    </div>
                    <i id="courses-arrow" class="fas fa-chevron-down text-sm transition-transform duration-200"></i>
                </button>
                <div id="mobile-courses-dropdown" class="hidden bg-gray-50">
                    <?php
                        $courses = \App\Models\Course::where('is_active', true)->take(3)->get();
                    ?>

                    <?php $__empty_1 = true; $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <a href="<?php echo e(route('courses.show', $course->slug)); ?>" class="flex items-center px-8 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors duration-200">
                            <i class="fas fa-book mr-3 text-xs"></i>
                            <span class="text-sm"><?php echo e($course->title); ?></span>
                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <a href="/courses" class="flex items-center px-8 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors duration-200">
                            <i class="fas fa-book mr-3 text-xs"></i>
                            <span class="text-sm">Arabic Language</span>
                        </a>
                        <a href="/courses" class="flex items-center px-8 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors duration-200">
                            <i class="fas fa-book-quran mr-3 text-xs"></i>
                            <span class="text-sm">Quran Memorization</span>
                        </a>
                    <?php endif; ?>
                    <a href="/courses" class="flex items-center px-8 py-2 text-blue-600 hover:bg-blue-50 transition-colors duration-200 font-medium">
                        <i class="fas fa-arrow-right mr-3 text-xs"></i>
                        <span class="text-sm">View All Courses</span>
                    </a>
                </div>
            </div>

            <a href="/teachers" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 border-b border-gray-100 transition-colors duration-200 <?php echo e(request()->is('teachers*') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                <i class="fas fa-chalkboard-teacher mr-3 text-blue-500"></i>
                <span class="font-medium">Teachers</span>
            </a>

            <a href="/pricing" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 border-b border-gray-100 transition-colors duration-200 <?php echo e(request()->is('pricing') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                <i class="fas fa-dollar-sign mr-3 text-blue-500"></i>
                <span class="font-medium">Pricing</span>
            </a>

            <a href="/blog" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 border-b border-gray-100 transition-colors duration-200 <?php echo e(request()->is('blog*') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                <i class="fas fa-blog mr-3 text-blue-500"></i>
                <span class="font-medium">Blog</span>
            </a>

            <a href="/books" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 border-b border-gray-100 transition-colors duration-200 <?php echo e(request()->is('books*') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                <i class="fas fa-book mr-3 text-blue-500"></i>
                <span class="font-medium">Books</span>
            </a>

            <a href="/about" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 border-b border-gray-100 transition-colors duration-200 <?php echo e(request()->is('about') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                <i class="fas fa-info-circle mr-3 text-blue-500"></i>
                <span class="font-medium">About</span>
            </a>

            <a href="/contact" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 border-b border-gray-100 transition-colors duration-200 <?php echo e(request()->is('contact') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                <i class="fas fa-envelope mr-3 text-blue-500"></i>
                <span class="font-medium">Contact</span>
            </a>

            <a href="/trial-class" class="flex items-center px-4 py-3 text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 mx-4 my-3 rounded-lg transition-all duration-200 shadow-md">
                <i class="fas fa-play-circle mr-3"></i>
                <span class="font-medium">Free Trial</span>
            </a>
        </div>

        <!-- Social Media -->
        <div class="px-4 py-4 border-t border-gray-200">
            <p class="text-sm font-medium text-gray-700 mb-3">Social media :</p>
            <div class="flex space-x-4">
                <a href="#" class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white hover:bg-blue-700 transition-colors duration-200">
                    <i class="fab fa-facebook-f"></i>
                </a>
                <a href="#" class="w-10 h-10 bg-pink-500 rounded-full flex items-center justify-center text-white hover:bg-pink-600 transition-colors duration-200">
                    <i class="fab fa-instagram"></i>
                </a>
                <a href="#" class="w-10 h-10 bg-black rounded-full flex items-center justify-center text-white hover:bg-gray-800 transition-colors duration-200">
                    <i class="fab fa-x-twitter"></i>
                </a>
                <a href="#" class="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center text-white hover:bg-red-700 transition-colors duration-200">
                    <i class="fab fa-tiktok"></i>
                </a>
                <a href="#" class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-colors duration-200">
                    <i class="fab fa-snapchat-ghost"></i>
                </a>
                <a href="#" class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center text-white hover:bg-red-600 transition-colors duration-200">
                    <i class="fab fa-youtube"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main>
        <?php echo e($slot); ?>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto container-padding py-16">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="lg:col-span-1">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-graduation-cap text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">Zajel Arabic Academy</h3>
                            <p class="text-gray-400 text-sm font-medium">Learn Arabic & Quran</p>
                        </div>
                    </div>
                    <p class="text-gray-300 leading-relaxed font-medium mb-6">
                        Empowering students worldwide to master Arabic language and Quran through innovative online education.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Quick Links</h4>
                    <ul class="space-y-3">
                        <li><a href="/courses" class="footer-link">Our Courses</a></li>
                        <li><a href="/trial-class" class="footer-link">Free Trial</a></li>
                        <li><a href="/pricing" class="footer-link">Pricing</a></li>
                        <li><a href="/about" class="footer-link">About Us</a></li>
                        <li><a href="/blog" class="footer-link">Blog</a></li>
                        <li><a href="/books" class="footer-link">Free Books</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Support</h4>
                    <ul class="space-y-3">
                        <li><a href="/contact" class="footer-link">Contact Us</a></li>
                        <li><a href="/faq" class="footer-link">FAQ</a></li>
                        <li><a href="#" class="footer-link">Help Center</a></li>
                        <li><a href="#" class="footer-link">Student Portal</a></li>
                        <li><a href="#" class="footer-link">Technical Support</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Contact Info</h4>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <i class="fas fa-envelope text-primary-400 mt-1"></i>
                            <div>
                                <p class="text-gray-300 font-medium"><EMAIL></p>
                                <p class="text-gray-400 text-sm">Email us anytime</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <i class="fas fa-phone text-primary-400 mt-1"></i>
                            <div>
                                <p class="text-gray-300 font-medium">+****************</p>
                                <p class="text-gray-400 text-sm">Call us for support</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <i class="fas fa-clock text-primary-400 mt-1"></i>
                            <div>
                                <p class="text-gray-300 font-medium">24/7 Support</p>
                                <p class="text-gray-400 text-sm">We're here to help</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Footer -->
        <div class="border-t border-gray-800">
            <div class="max-w-7xl mx-auto container-padding py-6">
                <div class="flex flex-col md:flex-row items-center justify-between">
                    <p class="text-gray-400 text-sm font-medium">
                        © <?php echo e(date('Y')); ?> Zajel Arabic Academy. All rights reserved.
                    </p>
                    <div class="flex items-center space-x-6 mt-4 md:mt-0">
                        <a href="/privacy-policy" class="text-gray-400 hover:text-white text-sm font-medium transition-colors duration-200">Privacy Policy</a>
                        <a href="/terms-of-service" class="text-gray-400 hover:text-white text-sm font-medium transition-colors duration-200">Terms of Service</a>
                        <a href="/cookie-policy" class="text-gray-400 hover:text-white text-sm font-medium transition-colors duration-200">Cookie Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating Action Buttons -->
    <div class="fixed bottom-6 right-6 z-50 flex flex-col space-y-4">
        <!-- WhatsApp Button -->
        <?php
            $whatsappNumber = \App\Models\Setting::get('whatsapp_number', '1234567890');
            $whatsappMessage = \App\Models\Setting::get('whatsapp_message', 'Hello! I would like to know more about your Arabic courses.');
        ?>
        <a href="https://wa.me/<?php echo e($whatsappNumber); ?>?text=<?php echo e(urlencode($whatsappMessage)); ?>" target="_blank" class="group w-14 h-14 bg-green-500 hover:bg-green-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300" title="Chat with us on WhatsApp">
            <i class="fab fa-whatsapp text-white text-2xl group-hover:scale-110 transition-transform duration-300"></i>
        </a>

        <!-- Back to Top Button -->
        <button id="back-to-top" class="w-14 h-14 bg-blue-600 hover:bg-blue-700 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300 opacity-0 invisible">
            <i class="fas fa-arrow-up text-white text-xl"></i>
        </button>
    </div>

    <!-- Mobile Menu JavaScript -->
    <script>
        // Mobile Menu Functions
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            const overlay = document.getElementById('mobile-overlay');

            if (menu && overlay) {
                const isOpen = !menu.classList.contains('-translate-x-full');

                if (isOpen) {
                    // Close menu
                    menu.classList.add('-translate-x-full');
                    overlay.classList.add('opacity-0', 'invisible');
                    overlay.classList.remove('opacity-100', 'visible');
                    document.body.classList.remove('overflow-hidden');
                } else {
                    // Open menu
                    menu.classList.remove('-translate-x-full');
                    overlay.classList.remove('opacity-0', 'invisible');
                    overlay.classList.add('opacity-100', 'visible');
                    document.body.classList.add('overflow-hidden');
                }
            }
        }

        function closeMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            const overlay = document.getElementById('mobile-overlay');

            if (menu && overlay) {
                menu.classList.add('-translate-x-full');
                overlay.classList.add('opacity-0', 'invisible');
                overlay.classList.remove('opacity-100', 'visible');
                document.body.classList.remove('overflow-hidden');
            }
        }

        // Dropdown toggles
        function toggleMobileCourses() {
            const dropdown = document.getElementById('mobile-courses-dropdown');
            const arrow = document.getElementById('courses-arrow');

            if (dropdown && arrow) {
                dropdown.classList.toggle('hidden');
                arrow.classList.toggle('rotate-180');
            }
        }

        // Make functions global
        window.toggleMobileMenu = toggleMobileMenu;
        window.closeMobileMenu = closeMobileMenu;
        window.toggleMobileCourses = toggleMobileCourses;

        document.addEventListener('DOMContentLoaded', function() {
            // Close menu when clicking overlay
            const overlay = document.getElementById('mobile-overlay');
            if (overlay) {
                overlay.addEventListener('click', closeMobileMenu);
            }

            // Close menu when clicking links
            const menuLinks = document.querySelectorAll('#mobile-menu a:not([onclick])');
            menuLinks.forEach(link => {
                link.addEventListener('click', closeMobileMenu);
            });
        });

        // Close menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMobileMenu();
            }
        });

        // Back to Top functionality
        const backToTopButton = document.getElementById('back-to-top');

        if (backToTopButton) {
            // Show/hide button based on scroll position
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.add('opacity-0', 'invisible');
                    backToTopButton.classList.remove('opacity-100', 'visible');
                }
            });

            // Smooth scroll to top
            backToTopButton.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // Set active navigation state
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('header .nav-link');

        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');

            // Exact match for active state
            if (href === currentPath) {
                link.classList.add('active');
            } else if (currentPath === '/' && href === '/') {
                link.classList.add('active');
            } else if (currentPath === '/pricing' && href === '/pricing') {
                link.classList.add('active');
            } else if (currentPath.startsWith('/courses') && href === '/courses') {
                link.classList.add('active');
            } else if (currentPath.startsWith('/blog') && href === '/blog') {
                link.classList.add('active');
            } else if (currentPath.startsWith('/books') && href === '/books') {
                link.classList.add('active');
            } else if (currentPath.startsWith('/about') && href === '/about') {
                link.classList.add('active');
            } else if (currentPath.startsWith('/contact') && href === '/contact') {
                link.classList.add('active');
            }
        });
    </script>
</body>
</html>
<?php /**PATH D:\laragon\www\zajelwebsite\resources\views/components/layouts/app.blade.php ENDPATH**/ ?>