<x-layouts.app>
    <x-slot name="title">{{ $heroTitle }} - Zajel Arabic Academy</x-slot>
    <x-slot name="metaDescription">{{ $heroSubtitle }}</x-slot>

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 overflow-hidden min-h-screen flex items-center">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-pulse"></div>
        <div class="absolute top-40 right-20 w-16 h-16 bg-yellow-400/20 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
        <div class="absolute bottom-40 left-20 w-12 h-12 bg-green-400/20 rounded-full animate-pulse" style="animation-delay: 2s;"></div>

        <div class="relative container mx-auto px-4 py-20">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <!-- Hero Content -->
                <div class="text-center lg:text-left space-y-8">
                    <div class="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-semibold text-white mb-6">
                        <i class="fas fa-star mr-2 text-yellow-300"></i>
                        #1 Arabic Learning Academy
                    </div>

                    <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight">
                        {{ $heroTitle }}
                    </h1>
                    <p class="text-xl md:text-2xl text-blue-100 leading-relaxed font-medium max-w-2xl">
                        {{ $heroSubtitle }}
                    </p>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                        <a href="/trial-class" class="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-all duration-200 shadow-xl transform hover:scale-105">
                            <i class="fas fa-play-circle mr-3"></i>
                            Start Free Trial
                        </a>
                        <a href="/courses" class="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-all duration-200">
                            <i class="fas fa-graduation-cap mr-3"></i>
                            Explore Courses
                        </a>
                    </div>

                    <!-- Stats -->
                    <div class="grid grid-cols-3 gap-8 mt-16 pt-8 border-t border-white/20">
                        <div class="text-center">
                            <div class="text-4xl md:text-5xl font-bold text-white mb-2 counter" data-target="1000">0</div>
                            <div class="text-blue-100 font-medium">Students</div>
                        </div>
                        <div class="text-center">
                            <div class="text-4xl md:text-5xl font-bold text-white mb-2 counter" data-target="50">0</div>
                            <div class="text-blue-100 font-medium">Courses</div>
                        </div>
                        <div class="text-center">
                            <div class="text-4xl md:text-5xl font-bold text-white mb-2 counter" data-target="15">0</div>
                            <div class="text-blue-100 font-medium">Instructors</div>
                        </div>
                    </div>
                </div>

                <!-- Hero Image/Video -->
                <div class="relative">
                    @if($heroVideoUrl)
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl">
                            <iframe
                                src="{{ $heroVideoUrl }}"
                                class="w-full h-64 md:h-80 lg:h-96"
                                frameborder="0"
                                allowfullscreen
                                title="Zajel Arabic Academy Introduction Video"
                            ></iframe>
                        </div>
                    @elseif($heroImage)
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl">
                            <img
                                src="{{ Storage::url($heroImage) }}"
                                alt="{{ $heroTitle }}"
                                class="w-full h-64 md:h-80 lg:h-96 object-cover"
                            >
                            <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                        </div>
                    @else
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl bg-white/10 backdrop-blur-sm border border-white/20">
                            <div class="flex items-center justify-center h-64 md:h-80 lg:h-96">
                                <div class="text-center text-white">
                                    <i class="fas fa-book-quran text-6xl mb-4 opacity-80"></i>
                                    <p class="text-xl font-semibold">Learn Quran & Arabic</p>
                                    <p class="text-blue-100">With Expert Instructors</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Floating Elements -->
                    <div class="absolute -top-4 -right-4 w-20 h-20 bg-accent-400 rounded-full opacity-80 animate-pulse"></div>
                    <div class="absolute -bottom-6 -left-6 w-16 h-16 bg-white rounded-full opacity-60 animate-pulse" style="animation-delay: 1s;"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="py-24 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute top-0 left-0 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div class="absolute top-0 right-0 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>

        <div class="container mx-auto px-4 relative">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20 items-center">
                <!-- About Image -->
                <div class="order-2 lg:order-1">
                    @if($aboutImage)
                        <div class="relative group">
                            <div class="absolute -inset-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                            <div class="relative rounded-3xl overflow-hidden shadow-2xl">
                                <img
                                    src="{{ Storage::url($aboutImage) }}"
                                    alt="{{ $aboutTitle }}"
                                    class="w-full h-96 lg:h-[500px] object-cover transform group-hover:scale-105 transition-transform duration-700"
                                >
                                <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                            </div>
                        </div>
                    @else
                        <div class="relative group">
                            <div class="absolute -inset-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                            <div class="relative rounded-3xl overflow-hidden shadow-2xl bg-gradient-to-br from-blue-500 via-blue-600 to-purple-700">
                                <div class="flex items-center justify-center h-96 lg:h-[500px] text-white">
                                    <div class="text-center space-y-6">
                                        <div class="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto backdrop-blur-sm">
                                            <i class="fas fa-mosque text-4xl"></i>
                                        </div>
                                        <div>
                                            <h3 class="text-3xl font-bold mb-2">Islamic Education</h3>
                                            <p class="text-blue-100 text-lg">Excellence in Learning</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- About Content -->
                <div class="order-1 lg:order-2 space-y-8">
                    <div class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 rounded-full text-sm font-semibold shadow-lg">
                        <i class="fas fa-info-circle mr-2"></i>
                        About Us
                    </div>

                    <h2 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                        {{ $aboutTitle }}
                    </h2>

                    <p class="text-xl text-gray-600 leading-relaxed font-medium">
                        {{ $aboutDescription }}
                    </p>

                    <!-- Mission & Vision -->
                    <div class="space-y-8 mb-10">
                        <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-14 h-14 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                                    <i class="fas fa-bullseye text-white text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold text-gray-900 mb-3">Our Mission</h3>
                                    <p class="text-gray-600 leading-relaxed font-medium text-lg">{{ $ourMission }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-14 h-14 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                                    <i class="fas fa-eye text-white text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold text-gray-900 mb-3">Our Vision</h3>
                                    <p class="text-gray-600 leading-relaxed font-medium text-lg">{{ $ourVision }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CTA Button -->
                    <div class="relative">
                        <a href="/about" class="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white text-lg font-semibold rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300 relative overflow-hidden">
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <i class="fas fa-arrow-right mr-3 group-hover:translate-x-1 transition-transform duration-300 relative z-10"></i>
                            <span class="relative z-10">Learn More About Us</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Courses Section -->
    <section class="py-24 bg-white">
        <div class="container mx-auto px-4">
            <!-- Section Header -->
            <div class="text-center mb-20">
                <div class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700 rounded-full text-sm font-semibold mb-8">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    Our Courses
                </div>
                <h2 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-8">
                    Featured Courses
                </h2>
                <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-medium">
                    Discover our most popular courses designed to help you master Arabic language and Quran recitation with expert guidance.
                </p>
            </div>

            <!-- Courses Carousel -->
            @php
                $allCourses = \App\Models\Course::where('is_active', true)->orderBy('is_featured', 'desc')->orderBy('created_at', 'desc')->get();
            @endphp

            @if($allCourses->count() > 0)
                <div class="relative">
                    <!-- Navigation Buttons -->
                    <div class="flex justify-between items-center mb-8">
                        <div class="flex space-x-4">
                            <button id="courses-prev" class="w-12 h-12 bg-white border-2 border-gray-200 rounded-full flex items-center justify-center text-gray-600 hover:text-blue-600 hover:border-blue-300 transition-all duration-200 shadow-md hover:shadow-lg">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button id="courses-next" class="w-12 h-12 bg-white border-2 border-gray-200 rounded-full flex items-center justify-center text-gray-600 hover:text-blue-600 hover:border-blue-300 transition-all duration-200 shadow-md hover:shadow-lg">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <div class="text-sm text-gray-500 font-medium">
                            <span id="current-slide">1</span> / <span id="total-slides">{{ $allCourses->count() }}</span>
                        </div>
                    </div>

                    <!-- Courses Container -->
                    <div class="overflow-hidden">
                        <div id="courses-container" class="flex transition-transform duration-500 ease-in-out" style="transform: translateX(0%)">
                            @foreach($allCourses as $course)
                                <div class="flex-none w-full md:w-1/2 lg:w-1/3 px-4">
                                    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl hover:border-blue-200 transition-all duration-300 group h-full">
                                        <!-- Course Image -->
                                        <div class="relative overflow-hidden rounded-t-xl">
                                            @if($course->image)
                                                <img
                                                    src="{{ Storage::url($course->image) }}"
                                                    alt="{{ $course->title }}"
                                                    class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                                >
                                            @else
                                                <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center">
                                                    <i class="fas fa-book-open text-white text-3xl"></i>
                                                </div>
                                            @endif

                                            <!-- Course Level Badge -->
                                            <div class="absolute top-4 left-4">
                                                <span class="px-3 py-1.5 bg-white text-blue-600 text-sm font-bold rounded-full shadow-lg border border-blue-100">
                                                    {{ ucfirst($course->level ?? 'Beginner') }}
                                                </span>
                                            </div>

                                            <!-- Course Price -->
                                            @if($course->price > 0)
                                                <div class="absolute top-4 right-4">
                                                    <div class="bg-gradient-to-r from-green-500 to-green-600 text-white px-3 py-1.5 rounded-full text-sm font-bold shadow-lg">
                                                        @if($course->discount_price)
                                                            <span class="line-through opacity-75 text-xs">${{ number_format($course->price, 0) }}</span>
                                                            <span class="ml-1">${{ number_format($course->discount_price, 0) }}</span>
                                                        @else
                                                            ${{ number_format($course->price, 0) }}
                                                        @endif
                                                    </div>
                                                </div>
                                            @else
                                                <div class="absolute top-4 right-4">
                                                    <span class="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-3 py-1.5 rounded-full text-sm font-bold shadow-lg">
                                                        Free
                                                    </span>
                                                </div>
                                            @endif

                                            <!-- Featured Badge -->
                                            @if($course->is_featured)
                                                <div class="absolute bottom-4 left-4">
                                                    <span class="bg-red-500 text-white px-2 py-1 rounded text-xs font-bold">
                                                        <i class="fas fa-star mr-1"></i>Featured
                                                    </span>
                                                </div>
                                            @endif
                                        </div>

                                        <!-- Course Content -->
                                        <div class="p-6 flex-1 flex flex-col">
                                            <!-- Category -->
                                            @if($course->category)
                                                <div class="flex items-center mb-3">
                                                    <span class="text-sm font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                                                        {{ $course->category->name }}
                                                    </span>
                                                </div>
                                            @endif

                                            <!-- Course Title -->
                                            <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-200">
                                                {{ $course->title }}
                                            </h3>

                                            <!-- Course Description -->
                                            <p class="text-gray-600 mb-4 leading-relaxed font-medium flex-1">
                                                {{ Str::limit(strip_tags($course->short_description ?: $course->description), 100) }}
                                            </p>

                                            <!-- Course Meta -->
                                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                                <div class="flex items-center">
                                                    <i class="fas fa-clock mr-2"></i>
                                                    <span class="font-medium">{{ $course->duration ?: '8 weeks' }}</span>
                                                </div>
                                                <div class="flex items-center">
                                                    <i class="fas fa-users mr-2"></i>
                                                    <span class="font-medium">{{ $course->max_students ?: 'Unlimited' }}</span>
                                                </div>
                                            </div>

                                            <!-- Course Link -->
                                            <a href="{{ route('courses.show', $course->slug) }}" class="block w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white text-center font-bold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-blue-800 transform hover:scale-105 transition-all duration-200 mt-auto">
                                                <i class="fas fa-graduation-cap mr-2"></i>
                                                View Course Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>

                </div>

                <!-- View All Courses Button -->
                <div class="text-center mt-12">
                    <a href="/courses" class="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-lg font-bold rounded-xl shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-blue-800 transform hover:scale-105 transition-all duration-200">
                        <i class="fas fa-th-large mr-3"></i>
                        View All Courses
                        <i class="fas fa-arrow-right ml-3"></i>
                    </a>
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-graduation-cap text-6xl text-gray-300 mb-4"></i>
                    <p class="text-xl text-gray-500 font-medium">No courses available at the moment.</p>
                </div>
            @endif
        </div>
    </section>

    <!-- Why Choose Zajel Arabic Academy Section -->
    <section class="py-24 bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23000000" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
        </div>

        <div class="container mx-auto px-4 relative">
            <!-- Section Header -->
            <div class="text-center mb-20">
                <div class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-full text-sm font-semibold mb-8 shadow-lg">
                    <i class="fas fa-star mr-2"></i>
                    Why Choose Us
                </div>
                <h2 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-8">
                    Why Choose <span class="text-gradient bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Zajel Arabic Academy</span>?
                </h2>
                <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-medium">
                    Discover what makes us the premier choice for Arabic language and Quran education worldwide.
                </p>
            </div>

            <!-- Features Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                <!-- Feature 1: Expert Instructors -->
                <div class="group">
                    <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                        <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-chalkboard-teacher text-white text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Expert Instructors</h3>
                        <p class="text-gray-600 leading-relaxed font-medium">
                            Learn from certified native Arabic speakers with years of teaching experience and deep knowledge of Islamic studies.
                        </p>
                    </div>
                </div>

                <!-- Feature 2: Flexible Learning -->
                <div class="group">
                    <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                        <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-clock text-white text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Flexible Schedule</h3>
                        <p class="text-gray-600 leading-relaxed font-medium">
                            Study at your own pace with flexible scheduling options that fit your busy lifestyle and time zone.
                        </p>
                    </div>
                </div>

                <!-- Feature 3: Interactive Learning -->
                <div class="group">
                    <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                        <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-laptop text-white text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Interactive Platform</h3>
                        <p class="text-gray-600 leading-relaxed font-medium">
                            Engage with cutting-edge learning technology, interactive exercises, and multimedia content.
                        </p>
                    </div>
                </div>

                <!-- Feature 4: Personalized Learning -->
                <div class="group">
                    <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                        <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-user-graduate text-white text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Personalized Learning</h3>
                        <p class="text-gray-600 leading-relaxed font-medium">
                            Customized curriculum tailored to your learning goals, current level, and preferred learning style.
                        </p>
                    </div>
                </div>

                <!-- Feature 5: Global Community -->
                <div class="group">
                    <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                        <div class="w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-globe text-white text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Global Community</h3>
                        <p class="text-gray-600 leading-relaxed font-medium">
                            Join thousands of students worldwide in our supportive learning community and cultural exchange.
                        </p>
                    </div>
                </div>

                <!-- Feature 6: Affordable Pricing -->
                <div class="group">
                    <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                        <div class="w-16 h-16 bg-gradient-to-r from-teal-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-dollar-sign text-white text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Affordable Pricing</h3>
                        <p class="text-gray-600 leading-relaxed font-medium">
                            High-quality education at competitive prices with flexible payment options and scholarship opportunities.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Stats Section -->
            <div class="bg-white rounded-3xl p-8 md:p-12 shadow-xl border border-gray-100">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-4xl md:text-5xl font-bold text-blue-600 mb-2" data-counter="5000">0</div>
                        <p class="text-gray-600 font-medium">Happy Students</p>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl md:text-5xl font-bold text-green-600 mb-2" data-counter="50">0</div>
                        <p class="text-gray-600 font-medium">Expert Teachers</p>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl md:text-5xl font-bold text-purple-600 mb-2" data-counter="100">0</div>
                        <p class="text-gray-600 font-medium">Countries Served</p>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl md:text-5xl font-bold text-orange-600 mb-2" data-counter="98">0</div>
                        <p class="text-gray-600 font-medium">Success Rate %</p>
                    </div>
                </div>
            </div>

            <!-- CTA Section -->
            <div class="text-center mt-16">
                <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-3xl p-8 md:p-12 text-white">
                    <h3 class="text-3xl md:text-4xl font-bold mb-6">Ready to Start Your Journey?</h3>
                    <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                        Join thousands of students who have transformed their lives through Arabic language and Quran education.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="/trial-class" class="btn bg-white text-blue-600 hover:bg-gray-100 text-lg font-semibold px-8 py-4 shadow-xl">
                            <i class="fas fa-play-circle mr-3"></i>
                            Start Free Trial
                        </a>
                        <a href="/courses" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-blue-600 text-lg font-semibold px-8 py-4">
                            <i class="fas fa-graduation-cap mr-3"></i>
                            Browse Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    @if($testimonials->count() > 0)
    <!-- Testimonials Section -->
    <section class="py-24 bg-gradient-to-br from-gray-50 to-gray-100">
        <div class="container mx-auto px-4">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <div class="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-semibold mb-6">
                    <i class="fas fa-quote-left mr-2"></i>
                    Testimonials
                </div>
                <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                    What Our Students Say
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
                    Hear from our students about their learning journey and success stories with Zajel Arabic Academy.
                </p>
            </div>

            <!-- Testimonials Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($testimonials as $testimonial)
                    <div class="card p-6 text-center">
                        <!-- Rating -->
                        <div class="flex justify-center mb-4">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star {{ $i <= $testimonial->rating ? 'text-yellow-400' : 'text-gray-300' }} text-lg"></i>
                            @endfor
                        </div>

                        <!-- Testimonial Text -->
                        <blockquote class="text-gray-700 mb-6 leading-relaxed font-medium italic">
                            "{{ $testimonial->comment }}"
                        </blockquote>

                        <!-- Student Info -->
                        <div class="flex items-center justify-center">
                            @if($testimonial->image)
                                <img
                                    src="{{ Storage::url($testimonial->image) }}"
                                    alt="{{ $testimonial->name }}"
                                    class="w-12 h-12 rounded-full object-cover mr-4"
                                >
                            @else
                                <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-user text-primary-600"></i>
                                </div>
                            @endif
                            <div class="text-left">
                                <div class="font-semibold text-gray-900">{{ $testimonial->name }}</div>
                                @if($testimonial->position || $testimonial->country)
                                    <div class="text-sm text-gray-500 font-medium">
                                        @if($testimonial->position){{ $testimonial->position }}@endif
                                        @if($testimonial->position && $testimonial->country), @endif
                                        @if($testimonial->country){{ $testimonial->country }}@endif
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    @if($latestPosts->count() > 0)
    <!-- Blog Section -->
    <section class="section-padding bg-white">
        <div class="max-w-7xl mx-auto container-padding">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <div class="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-semibold mb-6">
                    <i class="fas fa-blog mr-2"></i>
                    Latest Articles
                </div>
                <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                    From Our Blog
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
                    Stay updated with the latest insights, tips, and resources for learning Arabic and Quran.
                </p>
            </div>

            <!-- Blog Posts Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($latestPosts as $post)
                    <article class="card card-hover group">
                        <!-- Post Image -->
                        <div class="relative overflow-hidden">
                            @if($post->image)
                                <img
                                    src="{{ Storage::url($post->image) }}"
                                    alt="{{ $post->title }}"
                                    class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                >
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                    <i class="fas fa-newspaper text-gray-400 text-3xl"></i>
                                </div>
                            @endif
                        </div>

                        <!-- Post Content -->
                        <div class="p-6">
                            <!-- Post Meta -->
                            <div class="flex items-center text-sm text-gray-500 mb-3">
                                <time datetime="{{ $post->published_at->format('Y-m-d') }}" class="font-medium">
                                    {{ $post->published_at->format('M d, Y') }}
                                </time>
                                <span class="mx-2">•</span>
                                <span class="font-medium">{{ $post->reading_time }} min read</span>
                            </div>

                            <!-- Post Title -->
                            <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-200">
                                {{ $post->title }}
                            </h3>

                            <!-- Post Excerpt -->
                            <p class="text-gray-600 mb-4 leading-relaxed font-medium">
                                {{ Str::limit($post->excerpt ?: strip_tags($post->content), 120) }}
                            </p>

                            <!-- Read More Link -->
                            <a href="/blog/{{ $post->slug }}" class="inline-flex items-center text-primary-600 hover:text-primary-700 font-semibold transition-colors duration-200">
                                Read More
                                <i class="fas fa-arrow-right ml-2 text-sm"></i>
                            </a>
                        </div>
                    </article>
                @endforeach
            </div>

            <!-- View All Posts Button -->
            <div class="text-center mt-12">
                <a href="/blog" class="btn btn-outline text-lg font-semibold px-8 py-4">
                    <i class="fas fa-newspaper mr-2"></i>
                    View All Articles
                </a>
            </div>
        </div>
    </section>
    @endif

    <!-- CTA Section -->
    <section class="py-24 bg-gradient-to-br from-blue-600 to-blue-800 relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
        </div>

        <div class="container mx-auto px-4 text-center relative">
            <h2 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-8">
                Ready to Start Your Learning Journey?
            </h2>
            <p class="text-xl text-blue-100 mb-12 leading-relaxed font-medium max-w-3xl mx-auto">
                Join thousands of students who have transformed their lives through our comprehensive Arabic and Quran courses.
            </p>

            <div class="flex flex-col sm:flex-row gap-6 justify-center">
                <a href="/trial-class" class="inline-flex items-center justify-center px-10 py-5 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-all duration-200 shadow-xl transform hover:scale-105">
                    <i class="fas fa-play-circle mr-3 text-lg"></i>
                    Start Free Trial
                </a>
                <a href="/contact" class="inline-flex items-center justify-center px-10 py-5 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-all duration-200">
                    <i class="fas fa-envelope mr-3 text-lg"></i>
                    Contact Us
                </a>
            </div>
        </div>
    </section>

    <!-- Counter Animation Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const counters = document.querySelectorAll('.counter');
            const speed = 200; // Animation speed

            const animateCounter = (counter) => {
                const target = parseInt(counter.getAttribute('data-target'));
                const count = parseInt(counter.innerText);
                const increment = target / speed;

                if (count < target) {
                    counter.innerText = Math.ceil(count + increment);
                    setTimeout(() => animateCounter(counter), 1);
                } else {
                    counter.innerText = target + '+';
                }
            };

            // Intersection Observer to trigger animation when in view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const counter = entry.target;
                        animateCounter(counter);
                        observer.unobserve(counter); // Only animate once
                    }
                });
            }, { threshold: 0.5 });

            counters.forEach(counter => {
                observer.observe(counter);
            });
        });

        // Courses Carousel
        let currentSlide = 0;
        const coursesContainer = document.getElementById('courses-container');
        const totalSlides = document.getElementById('total-slides');
        const currentSlideSpan = document.getElementById('current-slide');
        const prevBtn = document.getElementById('courses-prev');
        const nextBtn = document.getElementById('courses-next');

        if (coursesContainer && totalSlides) {
            const totalCourses = parseInt(totalSlides.textContent);
            const slidesToShow = window.innerWidth >= 1024 ? 3 : window.innerWidth >= 768 ? 2 : 1;
            const maxSlide = Math.max(0, totalCourses - slidesToShow);

            function updateSlide() {
                const translateX = -(currentSlide * (100 / slidesToShow));
                coursesContainer.style.transform = `translateX(${translateX}%)`;
                currentSlideSpan.textContent = currentSlide + 1;

                // Update button states
                prevBtn.disabled = currentSlide === 0;
                nextBtn.disabled = currentSlide >= maxSlide;

                prevBtn.classList.toggle('opacity-50', currentSlide === 0);
                nextBtn.classList.toggle('opacity-50', currentSlide >= maxSlide);
            }

            prevBtn.addEventListener('click', () => {
                if (currentSlide > 0) {
                    currentSlide--;
                    updateSlide();
                }
            });

            nextBtn.addEventListener('click', () => {
                if (currentSlide < maxSlide) {
                    currentSlide++;
                    updateSlide();
                }
            });

            // Auto-play carousel
            setInterval(() => {
                if (currentSlide >= maxSlide) {
                    currentSlide = 0;
                } else {
                    currentSlide++;
                }
                updateSlide();
            }, 5000);

            // Initialize
            updateSlide();

            // Handle window resize
            window.addEventListener('resize', () => {
                const newSlidesToShow = window.innerWidth >= 1024 ? 3 : window.innerWidth >= 768 ? 2 : 1;
                if (newSlidesToShow !== slidesToShow) {
                    location.reload(); // Simple solution for responsive changes
                }
            });
        }
    </script>
</x-layouts.app>
