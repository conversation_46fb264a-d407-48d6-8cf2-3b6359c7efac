<x-layouts.app>
    <x-slot name="title">{{ $course->title }} - Zajel Arabic Academy</x-slot>
    <x-slot name="metaDescription">{{ $course->meta_description ?: Str::limit($course->description, 160) }}</x-slot>

    <!-- Course Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 text-white py-20 md:py-32 overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse"></div>
        <div class="absolute bottom-20 right-10 w-40 h-40 bg-yellow-400/20 rounded-full blur-2xl animate-pulse" style="animation-delay: 1s;"></div>

        <div class="max-w-7xl mx-auto container-padding relative">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <!-- Course Info -->
                <div class="space-y-8">
                    <!-- Breadcrumb -->
                    <nav class="flex items-center space-x-3 text-blue-200">
                        <a href="/" class="hover:text-white transition-colors font-medium text-sm">Home</a>
                        <i class="fas fa-chevron-right text-xs"></i>
                        <a href="/courses" class="hover:text-white transition-colors font-medium text-sm">Courses</a>
                        <i class="fas fa-chevron-right text-xs"></i>
                        <span class="text-white font-medium text-sm">{{ Str::limit($course->title, 30) }}</span>
                    </nav>

                    <!-- Category & Featured Badge -->
                    <div class="flex items-center space-x-4">
                        <div class="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-sm rounded-full text-sm font-bold border border-white/30">
                            <i class="{{ $course->category->icon ?? 'fas fa-book' }} mr-2"></i>
                            {{ $course->category->name }}
                        </div>
                        @if($course->is_featured)
                            <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-400 to-orange-400 text-gray-900 rounded-full text-sm font-bold">
                                <i class="fas fa-star mr-2"></i>
                                Featured
                            </div>
                        @endif
                    </div>

                    <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight">
                        {{ $course->title }}
                    </h1>

                    <p class="text-xl md:text-2xl text-blue-100 leading-relaxed font-medium">
                        {{ $course->short_description ?: Str::limit($course->description, 200) }}
                    </p>

                    <!-- Course Meta -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                        <div class="text-center">
                            <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-signal text-lg"></i>
                            </div>
                            <div class="text-sm text-blue-200 font-medium">Level</div>
                            <div class="font-semibold">{{ ucfirst($course->level) }}</div>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-clock text-lg"></i>
                            </div>
                            <div class="text-sm text-blue-200 font-medium">Duration</div>
                            <div class="font-semibold">{{ $course->duration ?: '8 weeks' }}</div>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-language text-lg"></i>
                            </div>
                            <div class="text-sm text-blue-200 font-medium">Language</div>
                            <div class="font-semibold">{{ $course->language }}</div>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-users text-lg"></i>
                            </div>
                            <div class="text-sm text-blue-200 font-medium">Students</div>
                            <div class="font-semibold">500+</div>
                        </div>
                    </div>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="/trial-class" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                            <i class="fas fa-play-circle mr-3"></i>
                            Start Free Trial
                        </a>
                        <a href="/contact" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 text-lg font-semibold px-8 py-4">
                            <i class="fas fa-envelope mr-3"></i>
                            Contact Us
                        </a>
                    </div>
                </div>

                <!-- Course Image -->
                <div class="relative">
                    @if($course->image)
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl">
                            <img
                                src="{{ Storage::url($course->image) }}"
                                alt="{{ $course->title }}"
                                class="w-full h-64 md:h-80 lg:h-96 object-cover"
                            >
                        </div>
                    @else
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl bg-white/10 backdrop-blur-sm border border-white/20">
                            <div class="flex items-center justify-center h-64 md:h-80 lg:h-96">
                                <div class="text-center text-white">
                                    <i class="fas fa-graduation-cap text-6xl mb-4 opacity-80"></i>
                                    <p class="text-xl font-semibold">{{ $course->title }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Price Badge -->
                    <div class="absolute top-4 right-4">
                        @if($course->price > 0)
                            <div class="bg-accent-500 text-white px-4 py-2 rounded-full font-bold shadow-lg">
                                @if($course->discount_price)
                                    <span class="line-through opacity-75 text-sm">${{ number_format($course->price, 0) }}</span>
                                    <span class="ml-2 text-lg">${{ number_format($course->discount_price, 0) }}</span>
                                @else
                                    <span class="text-lg">${{ number_format($course->price, 0) }}</span>
                                @endif
                            </div>
                        @else
                            <div class="bg-green-500 text-white px-4 py-2 rounded-full font-bold shadow-lg">
                                <span class="text-lg">Free</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Course Content -->
    <section class="section-padding bg-white">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <!-- Course Description -->
                    <div class="mb-12">
                        <h2 class="text-3xl font-bold text-gray-900 mb-6">Course Description</h2>
                        <div class="prose prose-lg max-w-none text-gray-700 leading-relaxed font-medium">
                            {!! nl2br(e($course->description)) !!}
                        </div>
                    </div>

                    <!-- What You'll Learn -->
                    @if($course->what_you_learn && count($course->what_you_learn) > 0)
                    <div class="mb-12">
                        <h2 class="text-3xl font-bold text-gray-900 mb-6">What You'll Learn</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($course->what_you_learn as $item)
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
                                        <i class="fas fa-check text-green-600 text-sm"></i>
                                    </div>
                                    <span class="text-gray-700 font-medium leading-relaxed">{{ $item['point'] ?? $item }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <!-- Course Content/Curriculum -->
                    @if($course->course_content && count($course->course_content) > 0)
                    <div class="mb-12">
                        <h2 class="text-3xl font-bold text-gray-900 mb-6">Course Curriculum</h2>
                        <div class="space-y-4">
                            @foreach($course->course_content as $index => $module)
                                <div class="border border-gray-200 rounded-lg overflow-hidden">
                                    <div class="bg-gray-50 px-6 py-4 flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <span class="w-8 h-8 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-semibold">
                                                {{ $index + 1 }}
                                            </span>
                                            <h3 class="text-lg font-semibold text-gray-900">{{ $module['title'] ?? 'Module ' . ($index + 1) }}</h3>
                                        </div>
                                        @if(isset($module['duration']))
                                            <span class="text-sm text-gray-500 font-medium">{{ $module['duration'] }}</span>
                                        @endif
                                    </div>
                                    @if(isset($module['description']))
                                        <div class="px-6 py-4">
                                            <p class="text-gray-700 leading-relaxed font-medium">{{ $module['description'] }}</p>
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <!-- Requirements -->
                    @if($course->requirements && count($course->requirements) > 0)
                    <div class="mb-12">
                        <h2 class="text-3xl font-bold text-gray-900 mb-6">Requirements</h2>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                            <ul class="space-y-3">
                                @foreach($course->requirements as $requirement)
                                    <li class="flex items-start space-x-3">
                                        <i class="fas fa-info-circle text-blue-600 mt-1"></i>
                                        <span class="text-gray-700 font-medium leading-relaxed">{{ $requirement['requirement'] ?? $requirement }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Instructor Card -->
                    <div class="card p-6 mb-8 sticky top-24">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Your Instructor</h3>

                        <div class="flex items-center space-x-4 mb-4">
                            @if($course->instructor_image)
                                <img
                                    src="{{ Storage::url($course->instructor_image) }}"
                                    alt="{{ $course->instructor_name }}"
                                    class="w-16 h-16 rounded-full object-cover"
                                >
                            @else
                                <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-primary-600 text-xl"></i>
                                </div>
                            @endif
                            <div>
                                <h4 class="text-lg font-semibold text-gray-900">{{ $course->instructor_name }}</h4>
                                <p class="text-gray-600 font-medium">Arabic & Quran Instructor</p>
                            </div>
                        </div>

                        @if($course->instructor_bio)
                            <p class="text-gray-700 leading-relaxed font-medium mb-6">
                                {{ $course->instructor_bio }}
                            </p>
                        @endif

                        <!-- Course Actions -->
                        <div class="space-y-3">
                            <a href="/trial-class" class="block w-full btn btn-primary text-center font-semibold">
                                <i class="fas fa-play-circle mr-2"></i>
                                Start Free Trial
                            </a>
                            <a href="/contact" class="block w-full btn btn-outline text-center font-semibold">
                                <i class="fas fa-envelope mr-2"></i>
                                Contact Instructor
                            </a>
                        </div>

                        <!-- Course Features -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <h4 class="font-semibold text-gray-900 mb-4">Course Features</h4>
                            <ul class="space-y-3 text-sm">
                                <li class="flex items-center space-x-3">
                                    <i class="fas fa-video text-primary-600"></i>
                                    <span class="text-gray-700 font-medium">Live Interactive Classes</span>
                                </li>
                                <li class="flex items-center space-x-3">
                                    <i class="fas fa-download text-primary-600"></i>
                                    <span class="text-gray-700 font-medium">Downloadable Resources</span>
                                </li>
                                <li class="flex items-center space-x-3">
                                    <i class="fas fa-certificate text-primary-600"></i>
                                    <span class="text-gray-700 font-medium">Certificate of Completion</span>
                                </li>
                                <li class="flex items-center space-x-3">
                                    <i class="fas fa-headset text-primary-600"></i>
                                    <span class="text-gray-700 font-medium">24/7 Support</span>
                                </li>
                                <li class="flex items-center space-x-3">
                                    <i class="fas fa-infinity text-primary-600"></i>
                                    <span class="text-gray-700 font-medium">Lifetime Access</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Student Testimonials -->
    @if($testimonials->count() > 0)
    <section class="section-padding bg-gray-50">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    What Students Say About This Course
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
                    Hear from students who have taken this course and achieved their learning goals.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($testimonials as $testimonial)
                    <div class="card p-6">
                        <!-- Rating -->
                        <div class="flex justify-center mb-4">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star {{ $i <= $testimonial->rating ? 'text-yellow-400' : 'text-gray-300' }} text-lg"></i>
                            @endfor
                        </div>

                        <!-- Testimonial Text -->
                        <blockquote class="text-gray-700 mb-6 leading-relaxed font-medium italic text-center">
                            "{{ $testimonial->comment }}"
                        </blockquote>

                        <!-- Student Info -->
                        <div class="flex items-center justify-center">
                            @if($testimonial->image)
                                <img
                                    src="{{ Storage::url($testimonial->image) }}"
                                    alt="{{ $testimonial->name }}"
                                    class="w-12 h-12 rounded-full object-cover mr-4"
                                >
                            @else
                                <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-user text-primary-600"></i>
                                </div>
                            @endif
                            <div class="text-left">
                                <div class="font-semibold text-gray-900">{{ $testimonial->name }}</div>
                                @if($testimonial->country)
                                    <div class="text-sm text-gray-500 font-medium">{{ $testimonial->country }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Related Courses -->
    @if($relatedCourses->count() > 0)
    <section class="section-padding bg-white">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    Related Courses
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
                    Explore other courses in the {{ $course->category->name }} category.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($relatedCourses as $relatedCourse)
                    <div class="card card-hover group">
                        <!-- Course Image -->
                        <div class="relative overflow-hidden">
                            @if($relatedCourse->image)
                                <img
                                    src="{{ Storage::url($relatedCourse->image) }}"
                                    alt="{{ $relatedCourse->title }}"
                                    class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                >
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center">
                                    <i class="fas fa-book-open text-white text-3xl"></i>
                                </div>
                            @endif

                            <!-- Course Level Badge -->
                            <div class="absolute top-4 left-4">
                                <span class="px-3 py-1 bg-white text-primary-600 text-sm font-semibold rounded-full shadow-md">
                                    {{ ucfirst($relatedCourse->level) }}
                                </span>
                            </div>
                        </div>

                        <!-- Course Content -->
                        <div class="p-6">
                            <!-- Course Title -->
                            <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-200">
                                {{ $relatedCourse->title }}
                            </h3>

                            <!-- Course Description -->
                            <p class="text-gray-600 mb-4 leading-relaxed font-medium">
                                {{ Str::limit($relatedCourse->short_description ?: $relatedCourse->description, 100) }}
                            </p>

                            <!-- Course Link -->
                            <a href="/courses/{{ $relatedCourse->slug }}" class="block w-full btn btn-primary text-center font-semibold">
                                <i class="fas fa-arrow-right mr-2"></i>
                                View Course
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- CTA Section -->
    <section class="section-padding bg-gradient-primary">
        <div class="max-w-4xl mx-auto container-padding text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Start This Course?
            </h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed font-medium">
                Join hundreds of students who have already started their learning journey with this course.
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/trial-class" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                    <i class="fas fa-play-circle mr-3"></i>
                    Start Free Trial
                </a>
                <a href="/contact" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 text-lg font-semibold px-8 py-4">
                    <i class="fas fa-envelope mr-3"></i>
                    Contact Us
                </a>
            </div>
        </div>
    </section>
</x-layouts.app>
