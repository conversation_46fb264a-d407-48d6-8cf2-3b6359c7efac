<x-layouts.app>
    @push('meta')
        <!-- SEO Meta Tags -->
        <title>{{ $course->title }} - Learn Arabic & Quran Online | Zajel Academy</title>
        <meta name="description" content="{{ Str::limit(strip_tags($course->description), 160) }}">
        <meta name="keywords" content="{{ $course->meta_keywords ?? 'Arabic course, Quran course, online learning, Islamic education, Arabic language' }}">
        <meta name="author" content="Zajel Academy">

        <!-- Open Graph Meta Tags -->
        <meta property="og:title" content="{{ $course->title }} - Zajel Academy">
        <meta property="og:description" content="{{ Str::limit(strip_tags($course->description), 160) }}">
        <meta property="og:image" content="{{ $course->image ? Storage::url($course->image) : asset('images/default-course.jpg') }}">
        <meta property="og:url" content="{{ url()->current() }}">
        <meta property="og:type" content="website">
        <meta property="og:site_name" content="Zajel Academy">

        <!-- Twitter Card Meta Tags -->
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="{{ $course->title }} - Zajel Academy">
        <meta name="twitter:description" content="{{ Str::limit(strip_tags($course->description), 160) }}">
        <meta name="twitter:image" content="{{ $course->image ? Storage::url($course->image) : asset('images/default-course.jpg') }}">

        <!-- Additional SEO -->
        <meta name="robots" content="index, follow">
        <link rel="canonical" href="{{ url()->current() }}">

        <!-- JSON-LD Structured Data -->
        <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Course",
            "name": "{{ $course->title }}",
            "description": "{{ strip_tags($course->description) }}",
            "provider": {
                "@type": "Organization",
                "name": "Zajel Academy",
                "url": "{{ url('/') }}"
            },
            "instructor": {
                "@type": "Person",
                "name": "{{ $course->teacher ? $course->teacher->name : 'Expert Instructor' }}"
            },
            "courseMode": "online",
            "educationalLevel": "{{ ucfirst($course->level) }}",
            "inLanguage": "ar",
            "offers": {
                "@type": "Offer",
                "price": "{{ $course->price }}",
                "priceCurrency": "USD",
                "availability": "https://schema.org/InStock"
            }
        }
        </script>
    @endpush
    <x-slot name="title">{{ $course->title }} - Zajel Arabic Academy</x-slot>
    <x-slot name="metaDescription">{{ $course->meta_description ?: Str::limit($course->description, 160) }}</x-slot>

    <!-- Course Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 text-white py-20 md:py-32 overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse"></div>
        <div class="absolute bottom-20 right-10 w-40 h-40 bg-yellow-400/20 rounded-full blur-2xl animate-pulse" style="animation-delay: 1s;"></div>

        <div class="max-w-7xl mx-auto container-padding relative">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <!-- Course Info -->
                <div class="space-y-8">
                    <!-- Breadcrumb -->
                    <nav class="flex items-center space-x-3 text-blue-200">
                        <a href="/" class="hover:text-white transition-colors font-medium text-sm">Home</a>
                        <i class="fas fa-chevron-right text-xs"></i>
                        <a href="/courses" class="hover:text-white transition-colors font-medium text-sm">Courses</a>
                        <i class="fas fa-chevron-right text-xs"></i>
                        <span class="text-white font-medium text-sm">{{ Str::limit($course->title, 30) }}</span>
                    </nav>

                    <!-- Category & Featured Badge -->
                    <div class="flex items-center space-x-4">
                        <div class="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-sm rounded-full text-sm font-bold border border-white/30">
                            <i class="{{ $course->category->icon ?? 'fas fa-book' }} mr-2"></i>
                            {{ $course->category->name }}
                        </div>
                        @if($course->is_featured)
                            <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-400 to-orange-400 text-gray-900 rounded-full text-sm font-bold">
                                <i class="fas fa-star mr-2"></i>
                                Featured
                            </div>
                        @endif
                    </div>

                    <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight">
                        {{ $course->title }}
                    </h1>

                    <p class="text-xl md:text-2xl text-blue-100 leading-relaxed font-medium">
                        {{ $course->short_description ?: Str::limit($course->description, 200) }}
                    </p>

                    <!-- Course Meta -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                        <div class="text-center">
                            <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-signal text-lg"></i>
                            </div>
                            <div class="text-sm text-blue-200 font-medium">Level</div>
                            <div class="font-semibold">{{ ucfirst($course->level) }}</div>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-clock text-lg"></i>
                            </div>
                            <div class="text-sm text-blue-200 font-medium">Duration</div>
                            <div class="font-semibold">{{ $course->duration ?: '8 weeks' }}</div>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-language text-lg"></i>
                            </div>
                            <div class="text-sm text-blue-200 font-medium">Language</div>
                            <div class="font-semibold">{{ $course->language }}</div>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-users text-lg"></i>
                            </div>
                            <div class="text-sm text-blue-200 font-medium">Students</div>
                            <div class="font-semibold">500+</div>
                        </div>
                    </div>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="/trial-class" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                            <i class="fas fa-play-circle mr-3"></i>
                            Start Free Trial
                        </a>
                        <a href="/contact" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 text-lg font-semibold px-8 py-4">
                            <i class="fas fa-envelope mr-3"></i>
                            Contact Us
                        </a>
                    </div>
                </div>

                <!-- Course Image -->
                <div class="relative">
                    @if($course->image)
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl">
                            <img
                                src="{{ Storage::url($course->image) }}"
                                alt="{{ $course->title }}"
                                class="w-full h-64 md:h-80 lg:h-96 object-cover"
                            >
                        </div>
                    @else
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl bg-white/10 backdrop-blur-sm border border-white/20">
                            <div class="flex items-center justify-center h-64 md:h-80 lg:h-96">
                                <div class="text-center text-white">
                                    <i class="fas fa-graduation-cap text-6xl mb-4 opacity-80"></i>
                                    <p class="text-xl font-semibold">{{ $course->title }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Price Badge -->
                    <div class="absolute top-4 right-4">
                        @if($course->price > 0)
                            <div class="bg-accent-500 text-white px-4 py-2 rounded-full font-bold shadow-lg">
                                @if($course->discount_price)
                                    <span class="line-through opacity-75 text-sm">${{ number_format($course->price, 0) }}</span>
                                    <span class="ml-2 text-lg">${{ number_format($course->discount_price, 0) }}</span>
                                @else
                                    <span class="text-lg">${{ number_format($course->price, 0) }}</span>
                                @endif
                            </div>
                        @else
                            <div class="bg-green-500 text-white px-4 py-2 rounded-full font-bold shadow-lg">
                                <span class="text-lg">Free</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Course Content -->
    <section class="py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-16">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-12">
                    <!-- Course Description -->
                    <div class="bg-white rounded-3xl p-8 md:p-12 shadow-xl border border-gray-100">
                        <div class="flex items-center mb-8">
                            <div class="w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mr-4">
                                <i class="fas fa-file-alt text-white text-xl"></i>
                            </div>
                            <h2 class="text-3xl md:text-4xl font-bold text-gray-900">Course Description</h2>
                        </div>

                        <div class="prose prose-lg max-w-none">
                            <div class="text-gray-700 leading-relaxed font-medium text-lg space-y-6">
                                {!! nl2br(e($course->description)) !!}
                            </div>
                        </div>

                        <!-- Course Highlights -->
                        <div class="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-100">
                            <h3 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-star text-blue-600 mr-3"></i>
                                Course Highlights
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                    <span class="font-medium">Expert Instruction</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                    <span class="font-medium">Interactive Learning</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                    <span class="font-medium">Flexible Schedule</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                    <span class="font-medium">Certificate of Completion</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- What You'll Learn -->
                    @if($course->what_you_learn && count($course->what_you_learn) > 0)
                    <div class="bg-white rounded-3xl p-8 md:p-12 shadow-xl border border-gray-100">
                        <div class="flex items-center mb-8">
                            <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mr-4">
                                <i class="fas fa-lightbulb text-white text-xl"></i>
                            </div>
                            <h2 class="text-3xl md:text-4xl font-bold text-gray-900">What You'll Learn</h2>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($course->what_you_learn as $item)
                                <div class="flex items-start space-x-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100">
                                    <div class="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
                                        <i class="fas fa-check text-white text-sm"></i>
                                    </div>
                                    <span class="text-gray-700 font-medium leading-relaxed text-lg">{{ $item['point'] ?? $item }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <!-- Course Content/Curriculum -->
                    @if($course->course_content && count($course->course_content) > 0)
                    <div class="bg-white rounded-3xl p-8 md:p-12 shadow-xl border border-gray-100">
                        <div class="flex items-center mb-8">
                            <div class="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mr-4">
                                <i class="fas fa-list-ul text-white text-xl"></i>
                            </div>
                            <h2 class="text-3xl md:text-4xl font-bold text-gray-900">Course Curriculum</h2>
                        </div>
                        <div class="space-y-6">
                            @foreach($course->course_content as $index => $module)
                                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-2xl overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                                    <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-6 flex items-center justify-between">
                                        <div class="flex items-center space-x-4">
                                            <span class="w-10 h-10 bg-white text-blue-600 rounded-full flex items-center justify-center text-lg font-bold shadow-lg">
                                                {{ $index + 1 }}
                                            </span>
                                            <h3 class="text-xl font-bold text-white">{{ $module['title'] ?? 'Module ' . ($index + 1) }}</h3>
                                        </div>
                                        @if(isset($module['duration']))
                                            <span class="bg-white bg-opacity-20 text-white px-4 py-2 rounded-full text-sm font-semibold">
                                                <i class="fas fa-clock mr-2"></i>{{ $module['duration'] }}
                                            </span>
                                        @endif
                                    </div>
                                    @if(isset($module['description']))
                                        <div class="px-8 py-6">
                                            <p class="text-gray-700 leading-relaxed font-medium text-lg">{{ $module['description'] }}</p>
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <!-- Requirements -->
                    @if($course->requirements && count($course->requirements) > 0)
                    <div class="bg-white rounded-3xl p-8 md:p-12 shadow-xl border border-gray-100">
                        <div class="flex items-center mb-8">
                            <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mr-4">
                                <i class="fas fa-exclamation-triangle text-white text-xl"></i>
                            </div>
                            <h2 class="text-3xl md:text-4xl font-bold text-gray-900">Requirements</h2>
                        </div>
                        <div class="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-2xl p-8">
                            <ul class="space-y-4">
                                @foreach($course->requirements as $requirement)
                                    <li class="flex items-start space-x-4">
                                        <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                            <i class="fas fa-info-circle text-white text-sm"></i>
                                        </div>
                                        <span class="text-gray-700 font-medium leading-relaxed text-lg">{{ $requirement['requirement'] ?? $requirement }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1 space-y-8">
                    <!-- Instructor Card -->
                    <div class="bg-white rounded-3xl p-8 shadow-xl border border-gray-100 sticky top-24">
                        <div class="flex items-center mb-6">
                            <div class="w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center mr-3">
                                <i class="fas fa-user-graduate text-white text-lg"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900">Your Instructor</h3>
                        </div>

                        <div class="flex items-center space-x-4 mb-6">
                            @if($course->teacher && $course->teacher->image)
                                <img
                                    src="{{ Storage::url($course->teacher->image) }}"
                                    alt="{{ $course->teacher->name }}"
                                    class="w-20 h-20 rounded-full object-cover border-4 border-gradient-to-r from-purple-400 to-pink-400"
                                >
                            @else
                                <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-2xl"></i>
                                </div>
                            @endif
                            <div>
                                <h4 class="text-xl font-bold text-gray-900">
                                    {{ $course->teacher ? $course->teacher->name : 'Expert Instructor' }}
                                </h4>
                                <p class="text-gray-600 font-medium">
                                    {{ $course->teacher ? $course->teacher->specialization : 'Arabic & Quran Instructor' }}
                                </p>
                            </div>
                        </div>

                        @if($course->teacher && $course->teacher->bio)
                            <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 mb-6 border border-purple-100">
                                <p class="text-gray-700 leading-relaxed font-medium">
                                    {{ $course->teacher->bio }}
                                </p>
                            </div>
                        @elseif($course->instructor_bio)
                            <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 mb-6 border border-purple-100">
                                <p class="text-gray-700 leading-relaxed font-medium">
                                    {{ $course->instructor_bio }}
                                </p>
                            </div>
                        @endif

                        <!-- Course Actions -->
                        <div class="space-y-4">
                            <a href="/trial-class" class="block w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white text-center font-bold py-4 px-6 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
                                <i class="fas fa-graduation-cap mr-3"></i>
                                Start Free Trial
                            </a>
                            <a href="/contact" class="block w-full bg-gradient-to-r from-gray-600 to-gray-700 text-white text-center font-bold py-4 px-6 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
                                <i class="fas fa-envelope mr-3"></i>
                                Contact Instructor
                            </a>
                        </div>

                    </div>

                    <!-- Course Features -->
                    <div class="bg-white rounded-3xl p-8 shadow-xl border border-gray-100">
                        <div class="flex items-center mb-6">
                            <div class="w-10 h-10 bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl flex items-center justify-center mr-3">
                                <i class="fas fa-star text-white text-lg"></i>
                            </div>
                            <h4 class="text-2xl font-bold text-gray-900">Course Features</h4>
                        </div>
                        <ul class="space-y-4">
                            <li class="flex items-center space-x-4 p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl border border-blue-100">
                                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-video text-white text-sm"></i>
                                </div>
                                <span class="text-gray-700 font-semibold">Live Interactive Classes</span>
                            </li>
                            <li class="flex items-center space-x-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100">
                                <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-download text-white text-sm"></i>
                                </div>
                                <span class="text-gray-700 font-semibold">Downloadable Resources</span>
                            </li>
                            <li class="flex items-center space-x-4 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border border-purple-100">
                                <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-certificate text-white text-sm"></i>
                                </div>
                                <span class="text-gray-700 font-semibold">Certificate of Completion</span>
                            </li>
                            <li class="flex items-center space-x-4 p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl border border-orange-100">
                                <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-headset text-white text-sm"></i>
                                </div>
                                <span class="text-gray-700 font-semibold">24/7 Support</span>
                            </li>
                            <li class="flex items-center space-x-4 p-4 bg-gradient-to-r from-indigo-50 to-blue-50 rounded-xl border border-indigo-100">
                                <div class="w-8 h-8 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-infinity text-white text-sm"></i>
                                </div>
                                <span class="text-gray-700 font-semibold">Lifetime Access</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Student Testimonials -->
    @if($testimonials->count() > 0)
    <section class="section-padding bg-gray-50">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    What Students Say About This Course
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
                    Hear from students who have taken this course and achieved their learning goals.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($testimonials as $testimonial)
                    <div class="card p-6">
                        <!-- Rating -->
                        <div class="flex justify-center mb-4">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star {{ $i <= $testimonial->rating ? 'text-yellow-400' : 'text-gray-300' }} text-lg"></i>
                            @endfor
                        </div>

                        <!-- Testimonial Text -->
                        <blockquote class="text-gray-700 mb-6 leading-relaxed font-medium italic text-center">
                            "{{ $testimonial->comment }}"
                        </blockquote>

                        <!-- Student Info -->
                        <div class="flex items-center justify-center">
                            @if($testimonial->image)
                                <img
                                    src="{{ Storage::url($testimonial->image) }}"
                                    alt="{{ $testimonial->name }}"
                                    class="w-12 h-12 rounded-full object-cover mr-4"
                                >
                            @else
                                <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-user text-primary-600"></i>
                                </div>
                            @endif
                            <div class="text-left">
                                <div class="font-semibold text-gray-900">{{ $testimonial->name }}</div>
                                @if($testimonial->country)
                                    <div class="text-sm text-gray-500 font-medium">{{ $testimonial->country }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Related Courses -->
    @if($relatedCourses->count() > 0)
    <section class="section-padding bg-white">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    Related Courses
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
                    Explore other courses in the {{ $course->category->name }} category.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($relatedCourses as $relatedCourse)
                    <div class="card card-hover group">
                        <!-- Course Image -->
                        <div class="relative overflow-hidden">
                            @if($relatedCourse->image)
                                <img
                                    src="{{ Storage::url($relatedCourse->image) }}"
                                    alt="{{ $relatedCourse->title }}"
                                    class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                >
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center">
                                    <i class="fas fa-book-open text-white text-3xl"></i>
                                </div>
                            @endif

                            <!-- Course Level Badge -->
                            <div class="absolute top-4 left-4">
                                <span class="px-3 py-1 bg-white text-primary-600 text-sm font-semibold rounded-full shadow-md">
                                    {{ ucfirst($relatedCourse->level) }}
                                </span>
                            </div>
                        </div>

                        <!-- Course Content -->
                        <div class="p-6">
                            <!-- Course Title -->
                            <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-200">
                                {{ $relatedCourse->title }}
                            </h3>

                            <!-- Course Description -->
                            <p class="text-gray-600 mb-4 leading-relaxed font-medium">
                                {{ Str::limit($relatedCourse->short_description ?: $relatedCourse->description, 100) }}
                            </p>

                            <!-- Course Link -->
                            <a href="/courses/{{ $relatedCourse->slug }}" class="block w-full btn btn-primary text-center font-semibold">
                                <i class="fas fa-arrow-right mr-2"></i>
                                View Course
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Course Reviews Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
                <!-- Reviews List -->
                <div class="lg:col-span-2">
                    <div class="flex items-center justify-between mb-8">
                        <h2 class="text-3xl font-bold text-gray-900">Student Reviews</h2>
                        @php
                            $reviews = $course->reviews()->approved()->latest()->get();
                            $averageRating = $reviews->avg('rating') ?: 0;
                            $totalReviews = $reviews->count();
                        @endphp
                        @if($totalReviews > 0)
                            <div class="flex items-center space-x-2">
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star {{ $i <= $averageRating ? 'text-yellow-400' : 'text-gray-300' }}"></i>
                                    @endfor
                                </div>
                                <span class="text-lg font-semibold text-gray-900">{{ number_format($averageRating, 1) }}</span>
                                <span class="text-gray-600">({{ $totalReviews }} {{ $totalReviews == 1 ? 'review' : 'reviews' }})</span>
                            </div>
                        @endif
                    </div>

                    @if($reviews->count() > 0)
                        <div class="space-y-6">
                            @foreach($reviews->take(5) as $review)
                                <div class="bg-white rounded-xl p-6 shadow-md border border-gray-100">
                                    <div class="flex items-start space-x-4">
                                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                            {{ strtoupper(substr($review->name, 0, 1)) }}
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-center justify-between mb-2">
                                                <h4 class="font-semibold text-gray-900">{{ $review->name }}</h4>
                                                <div class="flex items-center">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        <i class="fas fa-star text-sm {{ $i <= $review->rating ? 'text-yellow-400' : 'text-gray-300' }}"></i>
                                                    @endfor
                                                </div>
                                            </div>
                                            <p class="text-gray-600 leading-relaxed">{{ $review->review }}</p>
                                            <p class="text-sm text-gray-400 mt-2">{{ $review->created_at->diffForHumans() }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-12">
                            <i class="fas fa-star text-6xl text-gray-300 mb-4"></i>
                            <p class="text-xl text-gray-500 font-medium">No reviews yet. Be the first to review this course!</p>
                        </div>
                    @endif
                </div>

                <!-- Add Review Form -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 sticky top-8">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Write a Review</h3>

                        <form id="review-form" action="{{ route('courses.reviews.store', $course->slug) }}" method="POST" class="space-y-4">
                            @csrf

                            <div>
                                <label class="block text-sm font-semibold text-gray-700 mb-2">Your Rating</label>
                                <div class="flex items-center space-x-1 mb-4">
                                    @for($i = 1; $i <= 5; $i++)
                                        <button type="button" class="rating-star text-2xl text-gray-300 hover:text-yellow-400 transition-colors duration-200" data-rating="{{ $i }}">
                                            <i class="fas fa-star"></i>
                                        </button>
                                    @endfor
                                </div>
                                <input type="hidden" name="rating" id="rating-input" value="5" required>
                            </div>

                            <div>
                                <label for="name" class="block text-sm font-semibold text-gray-700 mb-2">Your Name *</label>
                                <input type="text" id="name" name="name" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">Your Email *</label>
                                <input type="email" id="email" name="email" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
                            </div>

                            <div>
                                <label for="review" class="block text-sm font-semibold text-gray-700 mb-2">Your Review *</label>
                                <textarea id="review" name="review" rows="4" required placeholder="Share your experience with this course..." class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 resize-none"></textarea>
                            </div>

                            <button type="submit" class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold py-3 px-6 rounded-lg hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Submit Review
                            </button>
                        </form>

                        <p class="text-xs text-gray-500 mt-4">
                            <i class="fas fa-info-circle mr-1"></i>
                            Your review will be published after approval.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-24 bg-gradient-to-br from-blue-800 via-blue-900 to-indigo-900 relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0">
            <div class="absolute top-10 left-10 w-32 h-32 bg-blue-400 rounded-full opacity-10 animate-pulse"></div>
            <div class="absolute bottom-10 right-10 w-24 h-24 bg-indigo-400 rounded-full opacity-10 animate-pulse delay-1000"></div>
            <div class="absolute top-1/2 left-1/4 w-16 h-16 bg-cyan-400 rounded-full opacity-10 animate-pulse delay-500"></div>
        </div>

        <div class="max-w-5xl mx-auto container-padding text-center relative z-10">
            <div class="mb-8">
                <div class="inline-flex items-center justify-center w-20 h-20 bg-white bg-opacity-20 rounded-full mb-6">
                    <i class="fas fa-rocket text-white text-3xl"></i>
                </div>
                <h2 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                    Ready to Start This <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-cyan-300">Course?</span>
                </h2>
                <p class="text-xl md:text-2xl text-blue-100 mb-12 leading-relaxed font-medium max-w-3xl mx-auto">
                    Join hundreds of students who have already started their learning journey with this course. Transform your skills today!
                </p>
            </div>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/trial-class" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                    <i class="fas fa-play-circle mr-3"></i>
                    Start Free Trial
                </a>
                <a href="/contact" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 text-lg font-semibold px-8 py-4">
                    <i class="fas fa-envelope mr-3"></i>
                    Contact Us
                </a>
            </div>
        </div>
    </section>

    <script>
        // Rating Stars Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const stars = document.querySelectorAll('.rating-star');
            const ratingInput = document.getElementById('rating-input');
            let currentRating = 5;

            stars.forEach((star, index) => {
                star.addEventListener('click', function() {
                    currentRating = index + 1;
                    ratingInput.value = currentRating;
                    updateStars();
                });

                star.addEventListener('mouseenter', function() {
                    highlightStars(index + 1);
                });
            });

            document.querySelector('.rating-star').closest('div').addEventListener('mouseleave', function() {
                updateStars();
            });

            function updateStars() {
                stars.forEach((star, index) => {
                    if (index < currentRating) {
                        star.classList.remove('text-gray-300');
                        star.classList.add('text-yellow-400');
                    } else {
                        star.classList.remove('text-yellow-400');
                        star.classList.add('text-gray-300');
                    }
                });
            }

            function highlightStars(rating) {
                stars.forEach((star, index) => {
                    if (index < rating) {
                        star.classList.remove('text-gray-300');
                        star.classList.add('text-yellow-400');
                    } else {
                        star.classList.remove('text-yellow-400');
                        star.classList.add('text-gray-300');
                    }
                });
            }

            // Initialize with 5 stars
            updateStars();
        });
    </script>
</x-layouts.app>
