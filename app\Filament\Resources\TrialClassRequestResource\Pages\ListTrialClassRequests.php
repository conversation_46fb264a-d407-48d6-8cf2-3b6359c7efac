<?php

namespace App\Filament\Resources\TrialClassRequestResource\Pages;

use App\Filament\Resources\TrialClassRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTrialClassRequests extends ListRecords
{
    protected static string $resource = TrialClassRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
