@php
    use App\Models\Setting;
    $siteName = Setting::get('site_name', 'Zajel Arabic Academy');
    $siteLogo = Setting::get('site_logo');
@endphp

<header class="bg-white shadow-soft sticky top-0 z-50 border-b border-gray-100">
    <nav class="max-w-7xl mx-auto container-padding" aria-label="Main navigation">
        <div class="flex justify-between items-center h-16 md:h-20">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
                <a href="{{ route('home') }}" class="flex items-center space-x-3 hover:opacity-80 transition-opacity duration-200">
                    @if($siteLogo)
                        <img src="{{ Storage::url($siteLogo) }}" alt="{{ $siteName }}" class="h-8 md:h-10 w-auto">
                    @else
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 md:w-10 md:h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                                <i class="fas fa-book-quran text-white text-lg md:text-xl"></i>
                            </div>
                            <span class="text-xl md:text-2xl font-bold text-gray-900 tracking-tight">{{ $siteName }}</span>
                        </div>
                    @endif
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden lg:flex lg:items-center lg:space-x-8">
                <a href="/" class="nav-link {{ request()->is('/') ? 'active' : '' }}">
                    <span class="text-base font-medium">Home</span>
                </a>
                <a href="/courses" class="nav-link {{ request()->is('courses*') ? 'active' : '' }}">
                    <span class="text-base font-medium">Courses</span>
                </a>
                <a href="/pricing" class="nav-link {{ request()->is('pricing') ? 'active' : '' }}">
                    <span class="text-base font-medium">Pricing</span>
                </a>
                <a href="/blog" class="nav-link {{ request()->is('blog*') ? 'active' : '' }}">
                    <span class="text-base font-medium">Blog</span>
                </a>
                <a href="/books" class="nav-link {{ request()->is('books*') ? 'active' : '' }}">
                    <span class="text-base font-medium">Books</span>
                </a>
                <a href="/about" class="nav-link {{ request()->is('about') ? 'active' : '' }}">
                    <span class="text-base font-medium">About Us</span>
                </a>
                <a href="/contact" class="nav-link {{ request()->is('contact') ? 'active' : '' }}">
                    <span class="text-base font-medium">Contact</span>
                </a>
            </div>

            <!-- CTA Buttons (Desktop) -->
            <div class="hidden lg:flex lg:items-center lg:space-x-4">
                <a href="/trial-class" class="btn btn-secondary text-sm font-semibold">
                    <i class="fas fa-play-circle mr-2"></i>
                    Free Trial
                </a>
                <a href="/courses" class="btn btn-primary text-sm font-semibold">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    Start Learning
                </a>
            </div>

            <!-- Mobile menu button -->
            <div class="lg:hidden">
                <button
                    type="button"
                    onclick="toggleMobileMenu()"
                    class="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-colors duration-200"
                    aria-expanded="false"
                    aria-label="Toggle main menu"
                >
                    <span class="sr-only">Open main menu</span>
                    <!-- Hamburger icon -->
                    <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>
    </nav>

    <!-- Mobile Navigation Menu -->
    <div id="mobile-menu" class="lg:hidden fixed inset-y-0 right-0 w-80 bg-white shadow-large transform translate-x-full transition-transform duration-300 ease-in-out z-50 overflow-y-auto">
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <div class="flex items-center space-x-2">
                @if($siteLogo)
                    <img src="{{ Storage::url($siteLogo) }}" alt="{{ $siteName }}" class="h-8 w-auto">
                @else
                    <div class="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                        <i class="fas fa-book-quran text-white text-sm"></i>
                    </div>
                    <span class="text-lg font-bold text-gray-900">{{ $siteName }}</span>
                @endif
            </div>
            <button
                type="button"
                onclick="toggleMobileMenu()"
                class="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
                aria-label="Close menu"
            >
                <span class="sr-only">Close menu</span>
                <!-- X icon -->
                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <div class="px-4 py-6 space-y-1">
            <a href="{{ route('home') }}" class="block px-3 py-3 text-base font-medium text-gray-900 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors duration-200 {{ request()->routeIs('home') ? 'text-primary-600 bg-primary-50' : '' }}">
                <i class="fas fa-home mr-3 text-sm"></i>
                Home
            </a>
            <a href="{{ route('courses.index') }}" class="block px-3 py-3 text-base font-medium text-gray-900 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors duration-200 {{ request()->routeIs('courses.*') ? 'text-primary-600 bg-primary-50' : '' }}">
                <i class="fas fa-graduation-cap mr-3 text-sm"></i>
                Courses
            </a>
            <a href="{{ route('pricing') }}" class="block px-3 py-3 text-base font-medium text-gray-900 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors duration-200 {{ request()->routeIs('pricing') ? 'text-primary-600 bg-primary-50' : '' }}">
                <i class="fas fa-tags mr-3 text-sm"></i>
                Pricing
            </a>
            <a href="{{ route('blog.index') }}" class="block px-3 py-3 text-base font-medium text-gray-900 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors duration-200 {{ request()->routeIs('blog.*') ? 'text-primary-600 bg-primary-50' : '' }}">
                <i class="fas fa-blog mr-3 text-sm"></i>
                Blog
            </a>
            <a href="{{ route('books.index') }}" class="block px-3 py-3 text-base font-medium text-gray-900 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors duration-200 {{ request()->routeIs('books.*') ? 'text-primary-600 bg-primary-50' : '' }}">
                <i class="fas fa-book mr-3 text-sm"></i>
                Books
            </a>
            <a href="{{ route('about') }}" class="block px-3 py-3 text-base font-medium text-gray-900 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors duration-200 {{ request()->routeIs('about') ? 'text-primary-600 bg-primary-50' : '' }}">
                <i class="fas fa-info-circle mr-3 text-sm"></i>
                About Us
            </a>
            <a href="{{ route('contact') }}" class="block px-3 py-3 text-base font-medium text-gray-900 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors duration-200 {{ request()->routeIs('contact') ? 'text-primary-600 bg-primary-50' : '' }}">
                <i class="fas fa-envelope mr-3 text-sm"></i>
                Contact
            </a>
        </div>

        <!-- Mobile CTA Buttons -->
        <div class="px-4 py-6 border-t border-gray-200 space-y-3">
            <a href="{{ route('trial-class') }}" class="block w-full btn btn-secondary text-center text-base font-semibold">
                <i class="fas fa-play-circle mr-2"></i>
                Free Trial Class
            </a>
            <a href="{{ route('courses.index') }}" class="block w-full btn btn-primary text-center text-base font-semibold">
                <i class="fas fa-graduation-cap mr-2"></i>
                Start Learning Now
            </a>
        </div>
    </div>
</header>
