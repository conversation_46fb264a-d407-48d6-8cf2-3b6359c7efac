@php
    use App\Models\Setting;
    $siteName = Setting::get('site_name', 'Zajel Arabic Academy');
    $siteLogo = Setting::get('site_logo');
@endphp

<header class="bg-white shadow-soft sticky top-0 z-50 border-b border-gray-100">
    <nav class="max-w-7xl mx-auto container-padding" aria-label="Main navigation">
        <div class="flex justify-between items-center h-16 md:h-20">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
                <a href="{{ route('home') }}" class="flex items-center space-x-3 hover:opacity-80 transition-opacity duration-200">
                    @if($siteLogo)
                        <img src="{{ Storage::url($siteLogo) }}" alt="{{ $siteName }}" class="h-8 md:h-10 w-auto">
                    @else
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 md:w-10 md:h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                                <i class="fas fa-book-quran text-white text-lg md:text-xl"></i>
                            </div>
                            <span class="text-xl md:text-2xl font-bold text-gray-900 tracking-tight">{{ $siteName }}</span>
                        </div>
                    @endif
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden lg:flex lg:items-center lg:space-x-8">
                <a href="/" class="nav-link {{ request()->is('/') ? 'active' : '' }}">
                    <span class="text-base font-medium">Home</span>
                </a>

                <!-- Courses Dropdown -->
                <div class="relative group">
                    <button class="nav-link {{ request()->is('courses*') ? 'active' : '' }} flex items-center">
                        <span class="text-base font-medium">Courses</span>
                        <i class="fas fa-chevron-down ml-1 text-xs transition-transform duration-200 group-hover:rotate-180"></i>
                    </button>

                    <!-- Dropdown Menu -->
                    <div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                        <div class="py-2">
                            <a href="/courses" class="block px-4 py-3 text-sm font-medium text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200">
                                <i class="fas fa-th-large mr-3 text-blue-500"></i>
                                All Courses
                            </a>
                            <div class="border-t border-gray-100 my-2"></div>
                            @php
                                $courses = \App\Models\Course::active()->featured()->limit(5)->get();
                            @endphp
                            @foreach($courses as $course)
                                <a href="/courses/{{ $course->slug }}" class="block px-4 py-3 text-sm font-medium text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200">
                                    <i class="fas fa-book mr-3 text-blue-500"></i>
                                    {{ $course->title }}
                                </a>
                            @endforeach
                        </div>
                    </div>
                </div>

                <a href="/pricing" class="nav-link {{ request()->is('pricing') ? 'active' : '' }}">
                    <span class="text-base font-medium">Pricing</span>
                </a>
                <a href="/blog" class="nav-link {{ request()->is('blog*') ? 'active' : '' }}">
                    <span class="text-base font-medium">Blog</span>
                </a>
                <a href="/books" class="nav-link {{ request()->is('books*') ? 'active' : '' }}">
                    <span class="text-base font-medium">Books</span>
                </a>
                <a href="/about" class="nav-link {{ request()->is('about') ? 'active' : '' }}">
                    <span class="text-base font-medium">About</span>
                </a>
                <a href="/contact" class="nav-link {{ request()->is('contact') ? 'active' : '' }}">
                    <span class="text-base font-medium">Contact</span>
                </a>
            </div>

            <!-- CTA Buttons (Desktop) -->
            <div class="hidden lg:flex lg:items-center lg:space-x-4">
                <a href="/trial-class" class="btn btn-secondary text-sm font-semibold">
                    <i class="fas fa-play-circle mr-2"></i>
                    Free Trial
                </a>
                <a href="/courses" class="btn btn-primary text-sm font-semibold">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    Start Learning
                </a>
            </div>

            <!-- Mobile menu button -->
            <div class="lg:hidden">
                <button
                    type="button"
                    onclick="toggleMobileMenu()"
                    class="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-colors duration-200"
                    aria-expanded="false"
                    aria-label="Toggle main menu"
                >
                    <span class="sr-only">Open main menu</span>
                    <!-- Hamburger icon -->
                    <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>
    </nav>

    <!-- Mobile Navigation Overlay -->
    <div id="mobile-overlay" class="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 opacity-0 invisible transition-all duration-300"></div>

    <!-- Mobile Navigation Menu -->
    <div id="mobile-menu" class="lg:hidden fixed inset-y-0 left-0 w-80 bg-white shadow-2xl transform -translate-x-full transition-transform duration-300 ease-in-out z-50 overflow-y-auto">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-700 p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    @if($siteLogo)
                        <img src="{{ Storage::url($siteLogo) }}" alt="{{ $siteName }}" class="h-8 w-auto">
                    @else
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-book-quran text-white text-lg"></i>
                        </div>
                        <span class="text-lg font-bold text-white">{{ $siteName }}</span>
                    @endif
                </div>
                <button
                    type="button"
                    onclick="toggleMobileMenu()"
                    class="inline-flex items-center justify-center p-2 rounded-md text-white hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-white transition-colors duration-200"
                    aria-label="Close menu"
                >
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>
        </div>

        <!-- All Departments Badge -->
        <div class="bg-yellow-400 text-center py-3">
            <span class="text-sm font-bold text-gray-900 uppercase tracking-wide">All Departments</span>
        </div>

        <!-- Navigation Links -->
        <div class="px-4 py-4">
            <a href="/" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 border-b border-gray-100 transition-colors duration-200 {{ request()->is('/') ? 'text-blue-600 bg-blue-50' : '' }}">
                <i class="fas fa-home mr-3 text-blue-500"></i>
                <span class="font-medium">Home</span>
            </a>

            <a href="/about" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 border-b border-gray-100 transition-colors duration-200 {{ request()->is('about') ? 'text-blue-600 bg-blue-50' : '' }}">
                <i class="fas fa-info-circle mr-3 text-blue-500"></i>
                <span class="font-medium">About us</span>
            </a>

            <!-- Courses with Dropdown -->
            <div class="border-b border-gray-100">
                <button onclick="toggleMobileCourses()" class="flex items-center justify-between w-full px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors duration-200 {{ request()->is('courses*') ? 'text-blue-600 bg-blue-50' : '' }}">
                    <div class="flex items-center">
                        <i class="fas fa-graduation-cap mr-3 text-blue-500"></i>
                        <span class="font-medium">Imam Courses</span>
                    </div>
                    <i class="fas fa-chevron-down text-sm transition-transform duration-200" id="courses-arrow"></i>
                </button>
                <div id="mobile-courses-dropdown" class="hidden bg-gray-50">
                    <a href="/courses" class="block px-12 py-2 text-sm text-gray-600 hover:text-blue-600">All Courses</a>
                    @php
                        $mobileCourses = \App\Models\Course::active()->featured()->limit(3)->get();
                    @endphp
                    @foreach($mobileCourses as $course)
                        <a href="/courses/{{ $course->slug }}" class="block px-12 py-2 text-sm text-gray-600 hover:text-blue-600">{{ $course->title }}</a>
                    @endforeach
                </div>
            </div>

            <!-- Plans / Fees -->
            <div class="border-b border-gray-100">
                <button onclick="toggleMobilePlans()" class="flex items-center justify-between w-full px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors duration-200 {{ request()->is('pricing') ? 'text-blue-600 bg-blue-50' : '' }}">
                    <div class="flex items-center">
                        <i class="fas fa-tags mr-3 text-blue-500"></i>
                        <span class="font-medium">Plans / Fees</span>
                    </div>
                    <i class="fas fa-chevron-down text-sm transition-transform duration-200" id="plans-arrow"></i>
                </button>
                <div id="mobile-plans-dropdown" class="hidden bg-gray-50">
                    <a href="/pricing" class="block px-12 py-2 text-sm text-gray-600 hover:text-blue-600">View Pricing</a>
                    <a href="/trial-class" class="block px-12 py-2 text-sm text-gray-600 hover:text-blue-600">Free Trial</a>
                </div>
            </div>

            <a href="/blog" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 border-b border-gray-100 transition-colors duration-200 {{ request()->is('blog*') ? 'text-blue-600 bg-blue-50' : '' }}">
                <i class="fas fa-blog mr-3 text-blue-500"></i>
                <span class="font-medium">Menu</span>
            </a>

            <a href="/contact" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 border-b border-gray-100 transition-colors duration-200 {{ request()->is('contact') ? 'text-blue-600 bg-blue-50' : '' }}">
                <i class="fas fa-envelope mr-3 text-blue-500"></i>
                <span class="font-medium">Contact</span>
            </a>

            <!-- Your Account -->
            <div class="border-b border-gray-100">
                <button onclick="toggleMobileAccount()" class="flex items-center justify-between w-full px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                    <div class="flex items-center">
                        <i class="fas fa-user mr-3 text-blue-500"></i>
                        <span class="font-medium">Your Account</span>
                    </div>
                    <i class="fas fa-chevron-down text-sm transition-transform duration-200" id="account-arrow"></i>
                </button>
                <div id="mobile-account-dropdown" class="hidden bg-gray-50">
                    <a href="/login" class="block px-12 py-2 text-sm text-gray-600 hover:text-blue-600">Login</a>
                    <a href="/register" class="block px-12 py-2 text-sm text-gray-600 hover:text-blue-600">Register</a>
                </div>
            </div>

            <a href="/trial-class" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-50 border-b border-gray-100 transition-colors duration-200 {{ request()->is('trial-class') ? 'text-blue-600 bg-blue-50' : '' }}">
                <i class="fas fa-play-circle mr-3 text-green-500"></i>
                <span class="font-medium">Free Trial</span>
            </a>
        </div>

        <!-- Social Media -->
        <div class="px-4 py-4 border-t border-gray-200">
            <p class="text-sm font-medium text-gray-700 mb-3">Social media :</p>
            <div class="flex space-x-4">
                <a href="#" class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white hover:bg-blue-700 transition-colors duration-200">
                    <i class="fab fa-facebook-f"></i>
                </a>
                <a href="#" class="w-10 h-10 bg-pink-500 rounded-full flex items-center justify-center text-white hover:bg-pink-600 transition-colors duration-200">
                    <i class="fab fa-instagram"></i>
                </a>
                <a href="#" class="w-10 h-10 bg-black rounded-full flex items-center justify-center text-white hover:bg-gray-800 transition-colors duration-200">
                    <i class="fab fa-x-twitter"></i>
                </a>
                <a href="#" class="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center text-white hover:bg-red-700 transition-colors duration-200">
                    <i class="fab fa-tiktok"></i>
                </a>
                <a href="#" class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-colors duration-200">
                    <i class="fab fa-snapchat-ghost"></i>
                </a>
                <a href="#" class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center text-white hover:bg-red-600 transition-colors duration-200">
                    <i class="fab fa-youtube"></i>
                </a>
            </div>
        </div>
    </div>
</header>
