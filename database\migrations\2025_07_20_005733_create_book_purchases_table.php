<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('book_purchases', function (Blueprint $table) {
            $table->id();
            $table->foreignId('book_id')->constrained()->onDelete('cascade');
            $table->string('customer_email');
            $table->string('customer_name');
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('USD');
            $table->string('payment_provider'); // stripe, paypal
            $table->string('payment_id'); // Transaction ID from provider
            $table->string('payment_status')->default('pending'); // pending, completed, failed, refunded
            $table->json('payment_data')->nullable(); // Store additional payment info
            $table->string('download_token')->unique(); // Secure token for download
            $table->timestamp('expires_at')->nullable(); // Download link expiry
            $table->integer('download_count')->default(0);
            $table->integer('max_downloads')->default(5);
            $table->timestamps();

            $table->index(['customer_email', 'book_id']);
            $table->index('payment_status');
            $table->index('download_token');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('book_purchases');
    }
};
