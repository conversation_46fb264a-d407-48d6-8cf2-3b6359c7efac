<?php if (isset($component)) { $__componentOriginal5863877a5171c196453bfa0bd807e410 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5863877a5171c196453bfa0bd807e410 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.app','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> Blog & Articles - Zajel Arabic Academy <?php $__env->endSlot(); ?>
     <?php $__env->slot('metaDescription', null, []); ?> Read our latest articles about Arabic language learning, Quran memorization, Islamic education, and tips for non-Arabic speakers. <?php $__env->endSlot(); ?>

    <!-- Page Header -->
    <section class="bg-gradient-primary text-white py-16 md:py-20">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                    Blog & Articles
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed font-medium">
                    Discover insights, tips, and resources to enhance your Arabic and Quran learning journey.
                </p>
            </div>
        </div>
    </section>

    <!-- Search and Filters -->
    <section class="bg-white border-b border-gray-200 sticky top-16 md:top-20 z-40">
        <div class="max-w-7xl mx-auto container-padding py-6">
            <form method="GET" class="flex flex-wrap items-center gap-4">
                <!-- Search -->
                <div class="flex-1 min-w-64">
                    <div class="relative">
                        <input
                            type="text"
                            name="search"
                            value="<?php echo e(request('search')); ?>"
                            placeholder="Search articles..."
                            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium"
                        >
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <!-- Tag Filter -->
                <?php if($popularTags->count() > 0): ?>
                <select name="tag" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium">
                    <option value="">All Topics</option>
                    <?php $__currentLoopData = $popularTags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($tag); ?>" <?php echo e(request('tag') === $tag ? 'selected' : ''); ?>>
                            <?php echo e(ucfirst($tag)); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <?php endif; ?>

                <!-- Sort -->
                <select name="sort" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium">
                    <option value="latest" <?php echo e(request('sort') === 'latest' ? 'selected' : ''); ?>>Latest</option>
                    <option value="popular" <?php echo e(request('sort') === 'popular' ? 'selected' : ''); ?>>Most Popular</option>
                    <option value="oldest" <?php echo e(request('sort') === 'oldest' ? 'selected' : ''); ?>>Oldest</option>
                </select>

                <!-- Filter Button -->
                <button type="submit" class="btn btn-primary font-semibold px-6 py-3">
                    <i class="fas fa-filter mr-2"></i>
                    Filter
                </button>

                <!-- Clear Filters -->
                <?php if(request()->hasAny(['search', 'tag', 'sort'])): ?>
                    <a href="/blog" class="btn btn-outline font-semibold px-6 py-3">
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </a>
                <?php endif; ?>
            </form>
        </div>
    </section>

    <div class="max-w-7xl mx-auto container-padding section-padding">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-3">
                <!-- Featured Posts -->
                <?php if($featuredPosts->count() > 0 && !request()->hasAny(['search', 'tag', 'sort'])): ?>
                <section class="mb-16">
                    <h2 class="text-3xl font-bold text-gray-900 mb-8">Featured Articles</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <?php $__currentLoopData = $featuredPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <article class="card card-hover group">
                                <!-- Post Image -->
                                <div class="relative overflow-hidden">
                                    <?php if($post->image): ?>
                                        <img
                                            src="<?php echo e(Storage::url($post->image)); ?>"
                                            alt="<?php echo e($post->title); ?>"
                                            class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                        >
                                    <?php else: ?>
                                        <div class="w-full h-48 bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center">
                                            <i class="fas fa-newspaper text-white text-3xl"></i>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Featured Badge -->
                                    <div class="absolute top-4 left-4">
                                        <span class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                            <i class="fas fa-star mr-1"></i>
                                            Featured
                                        </span>
                                    </div>
                                </div>

                                <!-- Post Content -->
                                <div class="p-6">
                                    <!-- Post Meta -->
                                    <div class="flex items-center text-sm text-gray-500 mb-3">
                                        <time datetime="<?php echo e($post->published_at->format('Y-m-d')); ?>" class="font-medium">
                                            <?php echo e($post->published_at->format('M d, Y')); ?>

                                        </time>
                                        <span class="mx-2">•</span>
                                        <span class="font-medium"><?php echo e($post->reading_time); ?> min read</span>
                                    </div>

                                    <!-- Post Title -->
                                    <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-200">
                                        <?php echo e($post->title); ?>

                                    </h3>

                                    <!-- Post Excerpt -->
                                    <p class="text-gray-600 mb-4 leading-relaxed font-medium">
                                        <?php echo e(Str::limit($post->excerpt ?: strip_tags($post->content), 120)); ?>

                                    </p>

                                    <!-- Read More Link -->
                                    <a href="/blog/<?php echo e($post->slug); ?>" class="inline-flex items-center text-primary-600 hover:text-primary-700 font-semibold transition-colors duration-200">
                                        Read More
                                        <i class="fas fa-arrow-right ml-2 text-sm"></i>
                                    </a>
                                </div>
                            </article>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </section>
                <?php endif; ?>

                <!-- All Posts -->
                <section>
                    <?php if(!request()->hasAny(['search', 'tag', 'sort'])): ?>
                        <h2 class="text-3xl font-bold text-gray-900 mb-8">Latest Articles</h2>
                    <?php endif; ?>

                    <?php if($posts->count() > 0): ?>
                        <!-- Results Info -->
                        <div class="flex items-center justify-between mb-8">
                            <p class="text-gray-600 font-medium">
                                Showing <?php echo e($posts->firstItem()); ?>-<?php echo e($posts->lastItem()); ?> of <?php echo e($posts->total()); ?> articles
                            </p>
                        </div>

                        <!-- Posts Grid -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                            <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <article class="card card-hover group">
                                    <!-- Post Image -->
                                    <div class="relative overflow-hidden">
                                        <?php if($post->image): ?>
                                            <img
                                                src="<?php echo e(Storage::url($post->image)); ?>"
                                                alt="<?php echo e($post->title); ?>"
                                                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                            >
                                        <?php else: ?>
                                            <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                                <i class="fas fa-newspaper text-gray-400 text-3xl"></i>
                                            </div>
                                        <?php endif; ?>

                                        <?php if($post->is_featured): ?>
                                            <div class="absolute top-4 left-4">
                                                <span class="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold">
                                                    <i class="fas fa-star mr-1"></i>
                                                    Featured
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Post Content -->
                                    <div class="p-6">
                                        <!-- Post Meta -->
                                        <div class="flex items-center text-sm text-gray-500 mb-3">
                                            <time datetime="<?php echo e($post->published_at->format('Y-m-d')); ?>" class="font-medium">
                                                <?php echo e($post->published_at->format('M d, Y')); ?>

                                            </time>
                                            <span class="mx-2">•</span>
                                            <span class="font-medium"><?php echo e($post->reading_time); ?> min read</span>
                                            <?php if($post->views_count > 0): ?>
                                                <span class="mx-2">•</span>
                                                <span class="font-medium"><?php echo e($post->views_count); ?> views</span>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Post Title -->
                                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-200">
                                            <?php echo e($post->title); ?>

                                        </h3>

                                        <!-- Post Excerpt -->
                                        <p class="text-gray-600 mb-4 leading-relaxed font-medium">
                                            <?php echo e(Str::limit($post->excerpt ?: strip_tags($post->content), 120)); ?>

                                        </p>

                                        <!-- Tags -->
                                        <?php if($post->tags && count($post->tags) > 0): ?>
                                            <div class="flex flex-wrap gap-2 mb-4">
                                                <?php $__currentLoopData = array_slice($post->tags, 0, 3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-medium rounded">
                                                        <?php echo e($tag); ?>

                                                    </span>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Author & Read More -->
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <?php if($post->author_image): ?>
                                                    <img
                                                        src="<?php echo e(Storage::url($post->author_image)); ?>"
                                                        alt="<?php echo e($post->author_name); ?>"
                                                        class="w-8 h-8 rounded-full object-cover mr-3"
                                                    >
                                                <?php else: ?>
                                                    <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                                                        <i class="fas fa-user text-primary-600 text-xs"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <span class="text-sm text-gray-600 font-medium"><?php echo e($post->author_name ?: 'Admin'); ?></span>
                                            </div>

                                            <a href="/blog/<?php echo e($post->slug); ?>" class="inline-flex items-center text-primary-600 hover:text-primary-700 font-semibold transition-colors duration-200">
                                                Read More
                                                <i class="fas fa-arrow-right ml-2 text-sm"></i>
                                            </a>
                                        </div>
                                    </div>
                                </article>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Pagination -->
                        <div class="flex justify-center">
                            <?php echo e($posts->appends(request()->query())->links()); ?>

                        </div>
                    <?php else: ?>
                        <!-- No Results -->
                        <div class="text-center py-16">
                            <i class="fas fa-search text-6xl text-gray-300 mb-6"></i>
                            <h3 class="text-2xl font-bold text-gray-900 mb-4">No articles found</h3>
                            <p class="text-lg text-gray-600 mb-8 max-w-md mx-auto">
                                We couldn't find any articles matching your criteria. Try adjusting your search terms or filters.
                            </p>
                            <a href="/blog" class="btn btn-primary font-semibold">
                                <i class="fas fa-refresh mr-2"></i>
                                View All Articles
                            </a>
                        </div>
                    <?php endif; ?>
                </section>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Popular Tags -->
                <?php if($popularTags->count() > 0): ?>
                <div class="card p-6 mb-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Popular Topics</h3>
                    <div class="flex flex-wrap gap-2">
                        <?php $__currentLoopData = $popularTags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a
                                href="/blog?tag=<?php echo e($tag); ?>"
                                class="px-3 py-2 bg-gray-100 hover:bg-primary-100 text-gray-700 hover:text-primary-700 text-sm font-medium rounded-lg transition-colors duration-200"
                            >
                                <?php echo e(ucfirst($tag)); ?>

                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Newsletter Signup -->
                <div class="card p-6 mb-8 bg-gradient-to-br from-primary-500 to-primary-700 text-white">
                    <h3 class="text-xl font-bold mb-4">Stay Updated</h3>
                    <p class="text-blue-100 mb-6 font-medium">
                        Subscribe to our newsletter for the latest articles and learning tips.
                    </p>
                    <form class="space-y-4">
                        <input
                            type="email"
                            placeholder="Your email address"
                            class="w-full px-4 py-3 rounded-lg text-gray-900 font-medium focus:ring-2 focus:ring-white focus:outline-none"
                        >
                        <button type="submit" class="w-full btn bg-white text-primary-600 hover:bg-gray-50 font-semibold">
                            <i class="fas fa-envelope mr-2"></i>
                            Subscribe
                        </button>
                    </form>
                </div>

                <!-- Learning Resources -->
                <div class="card p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Learning Resources</h3>
                    <ul class="space-y-3">
                        <li>
                            <a href="/courses" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium">
                                <i class="fas fa-graduation-cap mr-3 text-primary-600"></i>
                                Browse Courses
                            </a>
                        </li>
                        <li>
                            <a href="/books" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium">
                                <i class="fas fa-book mr-3 text-primary-600"></i>
                                Free Books
                            </a>
                        </li>
                        <li>
                            <a href="/trial-class" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium">
                                <i class="fas fa-play-circle mr-3 text-primary-600"></i>
                                Free Trial Class
                            </a>
                        </li>
                        <li>
                            <a href="/faq" class="flex items-center text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium">
                                <i class="fas fa-question-circle mr-3 text-primary-600"></i>
                                FAQ
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- CTA Section -->
    <section class="section-padding bg-gradient-primary">
        <div class="max-w-4xl mx-auto container-padding text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Start Learning?
            </h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed font-medium">
                Put what you've learned from our articles into practice with our comprehensive courses.
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/trial-class" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                    <i class="fas fa-play-circle mr-3"></i>
                    Start Free Trial
                </a>
                <a href="/courses" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 text-lg font-semibold px-8 py-4">
                    <i class="fas fa-graduation-cap mr-3"></i>
                    Browse Courses
                </a>
            </div>
        </div>
    </section>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $attributes = $__attributesOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__attributesOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $component = $__componentOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__componentOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\zajelwebsite\resources\views/blog/index.blade.php ENDPATH**/ ?>