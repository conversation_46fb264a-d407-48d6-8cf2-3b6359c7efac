<?php if (isset($component)) { $__componentOriginal5863877a5171c196453bfa0bd807e410 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5863877a5171c196453bfa0bd807e410 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.app','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> <?php echo e($heroTitle); ?> - Zajel Arabic Academy <?php $__env->endSlot(); ?>
     <?php $__env->slot('metaDescription', null, []); ?> <?php echo e($heroSubtitle); ?> <?php $__env->endSlot(); ?>

    <!-- Hero Section -->
    <section class="relative bg-gradient-primary overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
        </div>
        
        <div class="relative max-w-7xl mx-auto container-padding section-padding">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
                <!-- Hero Content -->
                <div class="text-center lg:text-left">
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                        <?php echo e($heroTitle); ?>

                    </h1>
                    <p class="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed font-medium">
                        <?php echo e($heroSubtitle); ?>

                    </p>
                    
                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                        <a href="/trial-class" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                            <i class="fas fa-play-circle mr-3"></i>
                            Start Free Trial
                        </a>
                        <a href="/courses" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 text-lg font-semibold px-8 py-4">
                            <i class="fas fa-graduation-cap mr-3"></i>
                            Explore Courses
                        </a>
                    </div>

                    <!-- Stats -->
                    <div class="grid grid-cols-3 gap-6 mt-12 pt-8 border-t border-blue-400 border-opacity-30">
                        <div class="text-center">
                            <div class="text-3xl md:text-4xl font-bold text-white mb-2">1000+</div>
                            <div class="text-blue-100 font-medium">Students</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl md:text-4xl font-bold text-white mb-2">50+</div>
                            <div class="text-blue-100 font-medium">Courses</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl md:text-4xl font-bold text-white mb-2">15+</div>
                            <div class="text-blue-100 font-medium">Instructors</div>
                        </div>
                    </div>
                </div>

                <!-- Hero Image/Video -->
                <div class="relative">
                    <?php if($heroVideoUrl): ?>
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl">
                            <iframe 
                                src="<?php echo e($heroVideoUrl); ?>" 
                                class="w-full h-64 md:h-80 lg:h-96"
                                frameborder="0" 
                                allowfullscreen
                                title="Zajel Arabic Academy Introduction Video"
                            ></iframe>
                        </div>
                    <?php elseif($heroImage): ?>
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl">
                            <img 
                                src="<?php echo e(Storage::url($heroImage)); ?>" 
                                alt="<?php echo e($heroTitle); ?>"
                                class="w-full h-64 md:h-80 lg:h-96 object-cover"
                            >
                            <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                        </div>
                    <?php else: ?>
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl bg-white/10 backdrop-blur-sm border border-white/20">
                            <div class="flex items-center justify-center h-64 md:h-80 lg:h-96">
                                <div class="text-center text-white">
                                    <i class="fas fa-book-quran text-6xl mb-4 opacity-80"></i>
                                    <p class="text-xl font-semibold">Learn Quran & Arabic</p>
                                    <p class="text-blue-100">With Expert Instructors</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Floating Elements -->
                    <div class="absolute -top-4 -right-4 w-20 h-20 bg-accent-400 rounded-full opacity-80 animate-pulse"></div>
                    <div class="absolute -bottom-6 -left-6 w-16 h-16 bg-white rounded-full opacity-60 animate-pulse" style="animation-delay: 1s;"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="section-padding bg-gray-50">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
                <!-- About Image -->
                <div class="order-2 lg:order-1">
                    <?php if($aboutImage): ?>
                        <div class="relative rounded-2xl overflow-hidden shadow-large">
                            <img 
                                src="<?php echo e(Storage::url($aboutImage)); ?>" 
                                alt="<?php echo e($aboutTitle); ?>"
                                class="w-full h-96 object-cover"
                            >
                        </div>
                    <?php else: ?>
                        <div class="relative rounded-2xl overflow-hidden shadow-large bg-gradient-to-br from-primary-500 to-primary-700">
                            <div class="flex items-center justify-center h-96 text-white">
                                <div class="text-center">
                                    <i class="fas fa-mosque text-6xl mb-4"></i>
                                    <p class="text-2xl font-semibold">Islamic Education</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- About Content -->
                <div class="order-1 lg:order-2">
                    <div class="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-semibold mb-6">
                        <i class="fas fa-info-circle mr-2"></i>
                        About Us
                    </div>
                    
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        <?php echo e($aboutTitle); ?>

                    </h2>
                    
                    <p class="text-lg text-gray-700 mb-8 leading-relaxed font-medium">
                        <?php echo e($aboutDescription); ?>

                    </p>

                    <!-- Mission & Vision -->
                    <div class="space-y-6 mb-8">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-bullseye text-primary-600 text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">Our Mission</h3>
                                <p class="text-gray-700 leading-relaxed font-medium"><?php echo e($ourMission); ?></p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-eye text-primary-600 text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">Our Vision</h3>
                                <p class="text-gray-700 leading-relaxed font-medium"><?php echo e($ourVision); ?></p>
                            </div>
                        </div>
                    </div>

                    <a href="/about" class="btn btn-primary text-lg font-semibold">
                        <i class="fas fa-arrow-right mr-2"></i>
                        Learn More About Us
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Courses Section -->
    <section class="section-padding bg-white">
        <div class="max-w-7xl mx-auto container-padding">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <div class="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-semibold mb-6">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    Our Courses
                </div>
                <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                    Featured Courses
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
                    Discover our most popular courses designed to help you master Arabic language and Quran recitation with expert guidance.
                </p>
            </div>

            <!-- Courses Grid -->
            <?php if($featuredCourses->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php $__currentLoopData = $featuredCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="card card-hover group">
                            <!-- Course Image -->
                            <div class="relative overflow-hidden">
                                <?php if($course->image): ?>
                                    <img 
                                        src="<?php echo e(Storage::url($course->image)); ?>" 
                                        alt="<?php echo e($course->title); ?>"
                                        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                    >
                                <?php else: ?>
                                    <div class="w-full h-48 bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center">
                                        <i class="fas fa-book-open text-white text-3xl"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Course Level Badge -->
                                <div class="absolute top-4 left-4">
                                    <span class="px-3 py-1 bg-white text-primary-600 text-sm font-semibold rounded-full shadow-md">
                                        <?php echo e(ucfirst($course->level)); ?>

                                    </span>
                                </div>
                                
                                <!-- Course Price -->
                                <?php if($course->price > 0): ?>
                                    <div class="absolute top-4 right-4">
                                        <div class="bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                            <?php if($course->discount_price): ?>
                                                <span class="line-through opacity-75">$<?php echo e(number_format($course->price, 0)); ?></span>
                                                <span class="ml-1">$<?php echo e(number_format($course->discount_price, 0)); ?></span>
                                            <?php else: ?>
                                                $<?php echo e(number_format($course->price, 0)); ?>

                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="absolute top-4 right-4">
                                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                            Free
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Course Content -->
                            <div class="p-6">
                                <!-- Category -->
                                <div class="flex items-center mb-3">
                                    <span class="text-sm font-medium text-primary-600 bg-primary-50 px-3 py-1 rounded-full">
                                        <?php echo e($course->category->name); ?>

                                    </span>
                                </div>

                                <!-- Course Title -->
                                <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-200">
                                    <?php echo e($course->title); ?>

                                </h3>

                                <!-- Course Description -->
                                <p class="text-gray-600 mb-4 leading-relaxed font-medium">
                                    <?php echo e(Str::limit($course->short_description ?: $course->description, 100)); ?>

                                </p>

                                <!-- Course Meta -->
                                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-clock mr-2"></i>
                                        <span class="font-medium"><?php echo e($course->duration ?: '8 weeks'); ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-user mr-2"></i>
                                        <span class="font-medium"><?php echo e($course->instructor_name); ?></span>
                                    </div>
                                </div>

                                <!-- Course Link -->
                                <a href="/courses/<?php echo e($course->slug); ?>" class="block w-full btn btn-primary text-center font-semibold">
                                    <i class="fas fa-arrow-right mr-2"></i>
                                    View Course
                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- View All Courses Button -->
                <div class="text-center mt-12">
                    <a href="/courses" class="btn btn-outline text-lg font-semibold px-8 py-4">
                        <i class="fas fa-th-large mr-2"></i>
                        View All Courses
                    </a>
                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <i class="fas fa-graduation-cap text-6xl text-gray-300 mb-4"></i>
                    <p class="text-xl text-gray-500 font-medium">No featured courses available at the moment.</p>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <?php if($testimonials->count() > 0): ?>
    <!-- Testimonials Section -->
    <section class="section-padding bg-gray-50">
        <div class="max-w-7xl mx-auto container-padding">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <div class="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-semibold mb-6">
                    <i class="fas fa-quote-left mr-2"></i>
                    Testimonials
                </div>
                <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                    What Our Students Say
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
                    Hear from our students about their learning journey and success stories with Zajel Arabic Academy.
                </p>
            </div>

            <!-- Testimonials Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="card p-6 text-center">
                        <!-- Rating -->
                        <div class="flex justify-center mb-4">
                            <?php for($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star <?php echo e($i <= $testimonial->rating ? 'text-yellow-400' : 'text-gray-300'); ?> text-lg"></i>
                            <?php endfor; ?>
                        </div>

                        <!-- Testimonial Text -->
                        <blockquote class="text-gray-700 mb-6 leading-relaxed font-medium italic">
                            "<?php echo e($testimonial->comment); ?>"
                        </blockquote>

                        <!-- Student Info -->
                        <div class="flex items-center justify-center">
                            <?php if($testimonial->image): ?>
                                <img 
                                    src="<?php echo e(Storage::url($testimonial->image)); ?>" 
                                    alt="<?php echo e($testimonial->name); ?>"
                                    class="w-12 h-12 rounded-full object-cover mr-4"
                                >
                            <?php else: ?>
                                <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-user text-primary-600"></i>
                                </div>
                            <?php endif; ?>
                            <div class="text-left">
                                <div class="font-semibold text-gray-900"><?php echo e($testimonial->name); ?></div>
                                <?php if($testimonial->position || $testimonial->country): ?>
                                    <div class="text-sm text-gray-500 font-medium">
                                        <?php if($testimonial->position): ?><?php echo e($testimonial->position); ?><?php endif; ?>
                                        <?php if($testimonial->position && $testimonial->country): ?>, <?php endif; ?>
                                        <?php if($testimonial->country): ?><?php echo e($testimonial->country); ?><?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <?php if($latestPosts->count() > 0): ?>
    <!-- Blog Section -->
    <section class="section-padding bg-white">
        <div class="max-w-7xl mx-auto container-padding">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <div class="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-semibold mb-6">
                    <i class="fas fa-blog mr-2"></i>
                    Latest Articles
                </div>
                <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                    From Our Blog
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
                    Stay updated with the latest insights, tips, and resources for learning Arabic and Quran.
                </p>
            </div>

            <!-- Blog Posts Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php $__currentLoopData = $latestPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <article class="card card-hover group">
                        <!-- Post Image -->
                        <div class="relative overflow-hidden">
                            <?php if($post->image): ?>
                                <img 
                                    src="<?php echo e(Storage::url($post->image)); ?>" 
                                    alt="<?php echo e($post->title); ?>"
                                    class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                >
                            <?php else: ?>
                                <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                    <i class="fas fa-newspaper text-gray-400 text-3xl"></i>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Post Content -->
                        <div class="p-6">
                            <!-- Post Meta -->
                            <div class="flex items-center text-sm text-gray-500 mb-3">
                                <time datetime="<?php echo e($post->published_at->format('Y-m-d')); ?>" class="font-medium">
                                    <?php echo e($post->published_at->format('M d, Y')); ?>

                                </time>
                                <span class="mx-2">•</span>
                                <span class="font-medium"><?php echo e($post->reading_time); ?> min read</span>
                            </div>

                            <!-- Post Title -->
                            <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-200">
                                <?php echo e($post->title); ?>

                            </h3>

                            <!-- Post Excerpt -->
                            <p class="text-gray-600 mb-4 leading-relaxed font-medium">
                                <?php echo e(Str::limit($post->excerpt ?: strip_tags($post->content), 120)); ?>

                            </p>

                            <!-- Read More Link -->
                            <a href="/blog/<?php echo e($post->slug); ?>" class="inline-flex items-center text-primary-600 hover:text-primary-700 font-semibold transition-colors duration-200">
                                Read More
                                <i class="fas fa-arrow-right ml-2 text-sm"></i>
                            </a>
                        </div>
                    </article>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- View All Posts Button -->
            <div class="text-center mt-12">
                <a href="/blog" class="btn btn-outline text-lg font-semibold px-8 py-4">
                    <i class="fas fa-newspaper mr-2"></i>
                    View All Articles
                </a>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- CTA Section -->
    <section class="section-padding bg-gradient-primary">
        <div class="max-w-4xl mx-auto container-padding text-center">
            <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
                Ready to Start Your Learning Journey?
            </h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed font-medium">
                Join thousands of students who have transformed their lives through our comprehensive Arabic and Quran courses.
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/trial-class" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                    <i class="fas fa-play-circle mr-3"></i>
                    Start Free Trial
                </a>
                <a href="/contact" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 text-lg font-semibold px-8 py-4">
                    <i class="fas fa-envelope mr-3"></i>
                    Contact Us
                </a>
            </div>
        </div>
    </section>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $attributes = $__attributesOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__attributesOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $component = $__componentOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__componentOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\zajelwebsite\resources\views/home.blade.php ENDPATH**/ ?>