<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="ltr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title ?? 'Zajel Arabic Academy - Learn Arabic & Quran Online' }}</title>
    <meta name="description" content="{{ $metaDescription ?? 'Learn Arabic language and Quran with expert instructors at Zajel Arabic Academy. Interactive online classes for all levels.' }}">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700|roboto:400,500,700|noto-sans-arabic:400,500,600,700|cairo:400,500,600,700" rel="stylesheet" />
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="flex items-center justify-between h-16 md:h-20">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-graduation-cap text-white text-xl"></i>
                        </div>
                        <div class="hidden sm:block">
                            <h1 class="text-xl font-bold text-gray-900">Zajel Arabic Academy</h1>
                            <p class="text-sm text-gray-600 font-medium">Learn Arabic & Quran</p>
                        </div>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex items-center space-x-8">
                    <a href="/" class="nav-link {{ request()->is('/') ? 'active' : '' }}">Home</a>
                    <a href="/courses" class="nav-link {{ request()->is('courses*') ? 'active' : '' }}">Courses</a>
                    <a href="/blog" class="nav-link {{ request()->is('blog*') ? 'active' : '' }}">Blog</a>
                    <a href="/books" class="nav-link {{ request()->is('books*') ? 'active' : '' }}">Books</a>
                    <a href="/about" class="nav-link {{ request()->is('about') ? 'active' : '' }}">About</a>
                    <a href="/contact" class="nav-link {{ request()->is('contact') ? 'active' : '' }}">Contact</a>
                </nav>

                <!-- CTA Buttons -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="/trial-class" class="btn btn-outline">
                        <i class="fas fa-play-circle mr-2"></i>
                        Free Trial
                    </a>
                    <a href="/pricing" class="btn btn-primary">
                        <i class="fas fa-rocket mr-2"></i>
                        Get Started
                    </a>
                </div>

                <!-- Mobile Menu Button -->
                <button 
                    id="mobile-menu-button"
                    class="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200"
                    aria-label="Open mobile menu"
                >
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 lg:hidden hidden">
        <!-- Mobile Menu Panel -->
        <div id="mobile-menu-panel" class="fixed top-0 right-0 h-full w-80 bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out">
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                        <i class="fas fa-graduation-cap text-white"></i>
                    </div>
                    <span class="text-lg font-bold text-gray-900">Zajel Academy</span>
                </div>
                <button id="mobile-menu-close" class="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <nav class="p-6">
                <div class="space-y-4">
                    <a href="/" class="mobile-nav-link {{ request()->is('/') ? 'active' : '' }}">
                        <i class="fas fa-home mr-3"></i>
                        Home
                    </a>
                    <a href="/courses" class="mobile-nav-link {{ request()->is('courses*') ? 'active' : '' }}">
                        <i class="fas fa-graduation-cap mr-3"></i>
                        Courses
                    </a>
                    <a href="/blog" class="mobile-nav-link {{ request()->is('blog*') ? 'active' : '' }}">
                        <i class="fas fa-blog mr-3"></i>
                        Blog
                    </a>
                    <a href="/books" class="mobile-nav-link {{ request()->is('books*') ? 'active' : '' }}">
                        <i class="fas fa-book mr-3"></i>
                        Books
                    </a>
                    <a href="/about" class="mobile-nav-link {{ request()->is('about') ? 'active' : '' }}">
                        <i class="fas fa-info-circle mr-3"></i>
                        About
                    </a>
                    <a href="/contact" class="mobile-nav-link {{ request()->is('contact') ? 'active' : '' }}">
                        <i class="fas fa-envelope mr-3"></i>
                        Contact
                    </a>
                </div>
                
                <div class="mt-8 pt-8 border-t border-gray-200 space-y-4">
                    <a href="/trial-class" class="block w-full btn btn-outline text-center">
                        <i class="fas fa-play-circle mr-2"></i>
                        Free Trial Class
                    </a>
                    <a href="/pricing" class="block w-full btn btn-primary text-center">
                        <i class="fas fa-rocket mr-2"></i>
                        Get Started
                    </a>
                </div>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <main>
        {{ $slot }}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto container-padding py-16">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="lg:col-span-1">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-graduation-cap text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">Zajel Arabic Academy</h3>
                            <p class="text-gray-400 text-sm font-medium">Learn Arabic & Quran</p>
                        </div>
                    </div>
                    <p class="text-gray-300 leading-relaxed font-medium mb-6">
                        Empowering students worldwide to master Arabic language and Quran through innovative online education.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors duration-200">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Quick Links</h4>
                    <ul class="space-y-3">
                        <li><a href="/courses" class="footer-link">Our Courses</a></li>
                        <li><a href="/trial-class" class="footer-link">Free Trial</a></li>
                        <li><a href="/pricing" class="footer-link">Pricing</a></li>
                        <li><a href="/about" class="footer-link">About Us</a></li>
                        <li><a href="/blog" class="footer-link">Blog</a></li>
                        <li><a href="/books" class="footer-link">Free Books</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Support</h4>
                    <ul class="space-y-3">
                        <li><a href="/contact" class="footer-link">Contact Us</a></li>
                        <li><a href="/faq" class="footer-link">FAQ</a></li>
                        <li><a href="#" class="footer-link">Help Center</a></li>
                        <li><a href="#" class="footer-link">Student Portal</a></li>
                        <li><a href="#" class="footer-link">Technical Support</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Contact Info</h4>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <i class="fas fa-envelope text-primary-400 mt-1"></i>
                            <div>
                                <p class="text-gray-300 font-medium"><EMAIL></p>
                                <p class="text-gray-400 text-sm">Email us anytime</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <i class="fas fa-phone text-primary-400 mt-1"></i>
                            <div>
                                <p class="text-gray-300 font-medium">+****************</p>
                                <p class="text-gray-400 text-sm">Call us for support</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <i class="fas fa-clock text-primary-400 mt-1"></i>
                            <div>
                                <p class="text-gray-300 font-medium">24/7 Support</p>
                                <p class="text-gray-400 text-sm">We're here to help</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Footer -->
        <div class="border-t border-gray-800">
            <div class="max-w-7xl mx-auto container-padding py-6">
                <div class="flex flex-col md:flex-row items-center justify-between">
                    <p class="text-gray-400 text-sm font-medium">
                        © {{ date('Y') }} Zajel Arabic Academy. All rights reserved.
                    </p>
                    <div class="flex items-center space-x-6 mt-4 md:mt-0">
                        <a href="/privacy-policy" class="text-gray-400 hover:text-white text-sm font-medium transition-colors duration-200">Privacy Policy</a>
                        <a href="/terms-of-service" class="text-gray-400 hover:text-white text-sm font-medium transition-colors duration-200">Terms of Service</a>
                        <a href="/cookie-policy" class="text-gray-400 hover:text-white text-sm font-medium transition-colors duration-200">Cookie Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Mobile Menu JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
            const mobileMenuPanel = document.getElementById('mobile-menu-panel');
            const mobileMenuClose = document.getElementById('mobile-menu-close');

            function openMobileMenu() {
                mobileMenuOverlay.classList.remove('hidden');
                setTimeout(() => {
                    mobileMenuPanel.classList.remove('translate-x-full');
                }, 10);
                document.body.style.overflow = 'hidden';
            }

            function closeMobileMenu() {
                mobileMenuPanel.classList.add('translate-x-full');
                setTimeout(() => {
                    mobileMenuOverlay.classList.add('hidden');
                }, 300);
                document.body.style.overflow = '';
            }

            mobileMenuButton.addEventListener('click', openMobileMenu);
            mobileMenuClose.addEventListener('click', closeMobileMenu);
            mobileMenuOverlay.addEventListener('click', function(e) {
                if (e.target === mobileMenuOverlay) {
                    closeMobileMenu();
                }
            });

            // Close menu on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !mobileMenuOverlay.classList.contains('hidden')) {
                    closeMobileMenu();
                }
            });
        });
    </script>
</body>
</html>
