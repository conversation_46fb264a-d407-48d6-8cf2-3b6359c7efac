<?php

namespace App\Http\Controllers;

use App\Models\TrialClassRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class TrialClassController extends Controller
{
    public function store(Request $request)
    {
        // Rate limiting - prevent spam
        $key = 'trial_request_' . $request->ip();
        if (cache()->has($key)) {
            return response()->json([
                'success' => false,
                'message' => 'Please wait before submitting another request. Try again in 5 minutes.',
            ], 429);
        }

        // Honeypot check - if filled, it's a bot
        if ($request->filled('website')) {
            Log::warning('Bot detected via honeypot', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Your request could not be submitted. Please try again.',
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255|regex:/^[a-zA-Z\s]+$/',
            'last_name' => 'required|string|max:255|regex:/^[a-zA-Z\s]+$/',
            'email' => 'required|email:rfc,dns|max:255',
            'phone' => 'nullable|string|max:20|regex:/^[+]?[0-9\s\-\(\)]+$/',
            'course_interest' => 'required|string|in:Arabic Language,Quran Recitation,Quran Memorization,Islamic Studies',
            'current_level' => 'required|string|in:Complete Beginner,Beginner,Intermediate,Advanced',
            'preferred_time' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:1000',
            'website' => 'nullable|max:0', // Honeypot should be empty
            'g-recaptcha-response' => 'nullable', // For future reCAPTCHA implementation
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please check your input and try again.',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check for duplicate submissions
        $fullName = trim($request->first_name . ' ' . $request->last_name);
        $existingRequest = TrialClassRequest::where('email', $request->email)
            ->where('created_at', '>=', now()->subHours(24))
            ->first();

        if ($existingRequest) {
            return response()->json([
                'success' => false,
                'message' => 'You have already submitted a trial request in the last 24 hours. Please check your email or contact us directly.',
            ], 409);
        }

        try {
            $trialRequest = TrialClassRequest::create([
                'name' => $fullName,
                'email' => strtolower(trim($request->email)),
                'phone' => $request->phone ? preg_replace('/[^+0-9]/', '', $request->phone) : null,
                'age_group' => $this->mapLevelToAgeGroup($request->current_level),
                'course_interest' => $this->mapCourseInterest($request->course_interest),
                'preferred_time' => $request->preferred_time,
                'notes' => $request->notes,
                'status' => 'pending',
                'is_read' => false,
            ]);

            // Set rate limit cache (5 minutes)
            cache()->put($key, true, now()->addMinutes(5));

            return response()->json([
                'success' => true,
                'message' => 'Thank you! Your trial class request has been submitted successfully. We will contact you within 24 hours to schedule your free session.',
                'data' => [
                    'id' => $trialRequest->id,
                    'name' => $trialRequest->name,
                    'email' => $trialRequest->email,
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('Trial class request error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong. Please try again or contact us directly.',
            ], 500);
        }
    }

    private function mapLevelToAgeGroup($level)
    {
        return match($level) {
            'Complete Beginner', 'Beginner' => 'adult',
            'Intermediate' => 'adult',
            'Advanced' => 'adult',
            default => 'adult'
        };
    }

    private function mapCourseInterest($interest)
    {
        return match($interest) {
            'Arabic Language' => 'arabic_basics',
            'Quran Recitation' => 'quran_recitation',
            'Quran Memorization' => 'quran_recitation',
            'Islamic Studies' => 'islamic_studies',
            default => 'arabic_basics'
        };
    }
}
