<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PricingPlan extends Model
{
    protected $fillable = [
        'name',
        'price',
        'original_price',
        'discount_percentage',
        'duration',
        'features',
        'button_text',
        'button_url',
        'is_popular',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'features' => 'array',
        'is_popular' => 'boolean',
        'is_active' => 'boolean',
        'price' => 'decimal:2',
        'original_price' => 'decimal:2'
    ];

    // Get features for display (always return simple array)
    public function getFeaturesForDisplayAttribute()
    {
        $features = $this->features;

        if (is_string($features)) {
            $features = json_decode($features, true) ?? [];
        }

        if (!is_array($features)) {
            return [];
        }

        // If it's Filament format (array of objects with 'feature' key)
        if (isset($features[0]['feature'])) {
            return collect($features)->pluck('feature')->toArray();
        }

        // If it's already a simple array
        return $features;
    }

    // Transform features for Filament
    public function getFeaturesAttribute($value)
    {
        $features = json_decode($value, true) ?? [];

        // For Filament admin, return in the expected format
        if (request()->is('admin/*')) {
            if (isset($features[0]['feature'])) {
                return $features;
            }

            return collect($features)->map(function ($feature) {
                return ['feature' => $feature];
            })->toArray();
        }

        // For frontend, return simple array
        if (isset($features[0]['feature'])) {
            return collect($features)->pluck('feature')->toArray();
        }

        return $features;
    }

    public function setFeaturesAttribute($value)
    {
        // If it's from Filament (array of objects with 'feature' key)
        if (is_array($value) && isset($value[0]['feature'])) {
            $this->attributes['features'] = json_encode(
                collect($value)->pluck('feature')->toArray()
            );
        } else {
            // If it's a simple array
            $this->attributes['features'] = json_encode($value);
        }
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    public function getFormattedPriceAttribute()
    {
        return '$' . number_format($this->price, 0);
    }

    public function getFormattedOriginalPriceAttribute()
    {
        return $this->original_price ? '$' . number_format($this->original_price, 0) : null;
    }

    public function hasDiscount()
    {
        return $this->discount_percentage > 0 && $this->original_price > 0;
    }
}
