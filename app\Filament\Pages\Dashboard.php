<?php

namespace App\Filament\Pages;

use App\Models\ContactSubmission;
use App\Models\TrialClassRequest;
use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Widgets\StatsOverviewWidget as BaseStatsOverviewWidget;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static ?string $title = 'Dashboard';

    protected static ?int $navigationSort = -2;

    public function getWidgets(): array
    {
        return [
            DashboardStatsWidget::class,
        ];
    }
}

class DashboardStatsWidget extends BaseStatsOverviewWidget
{
    protected function getStats(): array
    {
        return [
            BaseStatsOverviewWidget\Stat::make('Unread Contact Messages', ContactSubmission::unread()->count())
                ->description('New contact messages')
                ->descriptionIcon('heroicon-m-envelope')
                ->color('warning')
                ->url(route('filament.admin.resources.contact-submissions.index')),

            BaseStatsOverviewWidget\Stat::make('Pending Trial Requests', TrialClassRequest::unread()->count())
                ->description('New trial class requests')
                ->descriptionIcon('heroicon-m-academic-cap')
                ->color('danger')
                ->url(route('filament.admin.resources.trial-class-requests.index')),

            BaseStatsOverviewWidget\Stat::make('Total Contact Messages', ContactSubmission::count())
                ->description('All time')
                ->descriptionIcon('heroicon-m-chat-bubble-left-right')
                ->color('success'),

            BaseStatsOverviewWidget\Stat::make('Total Trial Requests', TrialClassRequest::count())
                ->description('All time')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),
        ];
    }
}
