@echo off
echo Starting Zajel Arabic Academy Development Environment...
cd /d "d:\laragon\www\zajelwebsite"

echo.
echo [1/3] Installing dependencies...
call composer install --no-interaction
call npm install

echo.
echo [2/3] Setting up environment...
if not exist .env (
    copy .env.example .env
    php artisan key:generate
)

echo.
echo [3/3] Building assets...
start /b npm run dev

echo.
echo ✅ Development environment ready!
echo.
echo Available commands:
echo - php artisan serve          (Start Laravel server)
echo - php artisan make:filament-user (Create admin user)
echo - npm run dev               (Watch assets)
echo.
echo Visit: http://localhost:8000 (Website)
echo Visit: http://localhost:8000/admin (Admin Panel)
echo.
pause
