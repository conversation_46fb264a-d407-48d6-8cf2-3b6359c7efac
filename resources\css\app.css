@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Google Fonts Import for Clear Typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap');

@layer base {
    /* Enhanced Typography for Maximum Clarity */
    html {
        font-family: 'Inter', 'Roboto', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
        font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
    }

    body {
        @apply font-sans text-base leading-relaxed text-gray-900 bg-white;
        font-weight: 400;
        letter-spacing: 0.025em;
        line-height: 1.7;
    }

    /* Arabic Text Optimization */
    .arabic-text {
        font-family: 'Noto Sans Arabic', 'Cairo', 'Amiri', sans-serif;
        direction: rtl;
        text-align: right;
        font-weight: 500;
        letter-spacing: 0.05em;
    }

    /* Enhanced Headings */
    h1, h2, h3, h4, h5, h6 {
        @apply font-display font-semibold text-gray-900;
        letter-spacing: 0.025em;
        line-height: 1.3;
        font-feature-settings: "kern" 1, "liga" 1;
    }

    h1 { @apply text-4xl md:text-5xl lg:text-6xl font-bold; }
    h2 { @apply text-3xl md:text-4xl lg:text-5xl font-bold; }
    h3 { @apply text-2xl md:text-3xl lg:text-4xl font-semibold; }
    h4 { @apply text-xl md:text-2xl lg:text-3xl font-semibold; }
    h5 { @apply text-lg md:text-xl lg:text-2xl font-medium; }
    h6 { @apply text-base md:text-lg lg:text-xl font-medium; }

    /* Enhanced Paragraphs */
    p {
        @apply text-base md:text-lg leading-relaxed text-gray-700;
        font-weight: 400;
        letter-spacing: 0.025em;
        margin-bottom: 1.25rem;
    }

    /* Enhanced Links */
    a {
        @apply text-primary-600 hover:text-primary-700 transition-colors duration-200;
        font-weight: 500;
        text-decoration: none;
    }

    /* Enhanced Buttons */
    .btn {
        @apply inline-flex items-center justify-center px-6 py-3 text-base font-semibold rounded-lg transition-all duration-200;
        letter-spacing: 0.025em;
        font-weight: 600;
    }

    .btn-primary {
        @apply bg-gradient-to-r from-primary-600 to-primary-700 text-white hover:from-primary-700 hover:to-primary-800 shadow-lg hover:shadow-xl;
    }

    .btn-secondary {
        @apply bg-white text-primary-600 border-2 border-primary-600 hover:bg-primary-50 hover:border-primary-700;
    }

    .btn-outline {
        @apply bg-transparent text-gray-700 border-2 border-gray-300 hover:bg-gray-50 hover:border-gray-400;
    }
}

@layer components {
    /* Navigation Components */
    .nav-link {
        @apply text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200 px-3 py-2 rounded-md;
        font-weight: 500;
        letter-spacing: 0.025em;
    }

    .nav-link.active {
        @apply text-primary-600 bg-primary-50;
    }

    /* Card Components */
    .card {
        @apply bg-white rounded-xl shadow-soft border border-gray-100 overflow-hidden;
    }

    .card-hover {
        @apply hover:shadow-medium transition-shadow duration-300;
    }

    /* Section Spacing */
    .section-padding {
        @apply py-16 md:py-20 lg:py-24;
    }

    .container-padding {
        @apply px-4 sm:px-6 lg:px-8;
    }

    /* Text Utilities for Enhanced Readability */
    .text-readable {
        @apply text-gray-700 leading-relaxed;
        font-weight: 400;
        letter-spacing: 0.025em;
    }

    .text-emphasis {
        @apply text-gray-900 font-semibold;
        letter-spacing: 0.025em;
    }

    /* Gradient Backgrounds */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 50%, #2563eb 100%);
    }

    .bg-gradient-secondary {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }
}
