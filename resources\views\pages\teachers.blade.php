<x-layouts.app>
    <x-slot name="title">Our Expert Teachers - Zajel Arabic Academy</x-slot>
    <x-slot name="metaDescription">Meet our qualified Arabic and Quran instructors. Experienced teachers with proven track records in teaching Arabic language and Quran to non-Arabic speakers worldwide.</x-slot>

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-600 to-blue-800 text-white py-20">
        <div class="container mx-auto px-4">
            <div class="text-center max-w-4xl mx-auto">
                <div class="inline-flex items-center px-4 py-2 bg-blue-500 bg-opacity-30 rounded-full text-sm font-semibold mb-6">
                    <i class="fas fa-chalkboard-teacher mr-2"></i>
                    Expert Instructors
                </div>
                
                <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    Meet Our <span class="text-yellow-400">Expert Teachers</span>
                </h1>
                
                <p class="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed font-medium">
                    Learn from qualified instructors with years of experience in teaching Arabic language and Quran to students worldwide.
                </p>

                <div class="flex flex-wrap justify-center gap-6 text-blue-100">
                    <div class="flex items-center">
                        <i class="fas fa-certificate text-yellow-400 mr-2"></i>
                        <span class="font-medium">Certified Instructors</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-globe text-yellow-400 mr-2"></i>
                        <span class="font-medium">International Experience</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-heart text-yellow-400 mr-2"></i>
                        <span class="font-medium">Passionate Educators</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Teachers Section -->
    @if($featuredTeachers->count() > 0)
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <div class="inline-flex items-center px-4 py-2 bg-yellow-100 text-yellow-700 rounded-full text-sm font-semibold mb-6">
                    <i class="fas fa-star mr-2"></i>
                    Featured Teachers
                </div>
                
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    Our Top Instructors
                </h2>
                
                <p class="text-xl text-gray-600 leading-relaxed font-medium max-w-2xl mx-auto">
                    Meet our most experienced and highly-rated teachers who have helped thousands of students achieve their learning goals.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($featuredTeachers as $teacher)
                    <div class="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                        <!-- Teacher Image -->
                        <div class="relative overflow-hidden">
                            <img 
                                src="{{ $teacher->image_url }}" 
                                alt="{{ $teacher->name }}"
                                class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                            >
                            <div class="absolute top-4 right-4">
                                <div class="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-sm font-bold flex items-center">
                                    <i class="fas fa-star mr-1"></i>
                                    {{ $teacher->rating }}
                                </div>
                            </div>
                        </div>

                        <!-- Teacher Info -->
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $teacher->name }}</h3>
                            <p class="text-blue-600 font-semibold mb-3">{{ $teacher->title }}</p>
                            
                            <p class="text-gray-700 mb-4 leading-relaxed">
                                {{ Str::limit($teacher->bio, 120) }}
                            </p>

                            <!-- Specializations -->
                            <div class="flex flex-wrap gap-2 mb-4">
                                @foreach(array_slice($teacher->specializations ?? [], 0, 2) as $specialization)
                                    <span class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                                        {{ $specialization }}
                                    </span>
                                @endforeach
                            </div>

                            <!-- Stats -->
                            <div class="flex items-center justify-between text-sm text-gray-600 mb-4">
                                <div class="flex items-center">
                                    <i class="fas fa-clock mr-1"></i>
                                    {{ $teacher->experience_text }}
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-users mr-1"></i>
                                    {{ $teacher->total_students }}+ students
                                </div>
                            </div>

                            <!-- Languages -->
                            <div class="text-sm text-gray-600 mb-4">
                                <i class="fas fa-language mr-1"></i>
                                Speaks: {{ $teacher->languages_string }}
                            </div>

                            <!-- CTA Button -->
                            <a href="/trial-class" class="block w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-center py-3 rounded-lg font-semibold transition-all duration-200 shadow-md hover:shadow-lg">
                                <i class="fas fa-calendar-plus mr-2"></i>
                                Book Trial with {{ explode(' ', $teacher->name)[0] }}
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- All Teachers Section -->
    @if($allTeachers->count() > 0)
    <section class="py-20">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                    All Our Teachers
                </h2>
                
                <p class="text-xl text-gray-600 leading-relaxed font-medium max-w-2xl mx-auto">
                    Choose from our diverse team of qualified instructors, each bringing unique expertise and teaching styles.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($allTeachers as $teacher)
                    <div class="bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden group hover:shadow-lg transition-all duration-300">
                        <!-- Teacher Image -->
                        <div class="relative overflow-hidden">
                            <img 
                                src="{{ $teacher->image_url }}" 
                                alt="{{ $teacher->name }}"
                                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                            >
                            <div class="absolute top-3 right-3">
                                <div class="bg-white bg-opacity-90 text-gray-800 px-2 py-1 rounded-full text-xs font-bold flex items-center">
                                    <i class="fas fa-star text-yellow-500 mr-1"></i>
                                    {{ $teacher->rating }}
                                </div>
                            </div>
                        </div>

                        <!-- Teacher Info -->
                        <div class="p-4">
                            <h3 class="text-lg font-bold text-gray-900 mb-1">{{ $teacher->name }}</h3>
                            <p class="text-blue-600 font-medium text-sm mb-2">{{ $teacher->title }}</p>
                            
                            <!-- Primary Specialization -->
                            @if($teacher->specializations && count($teacher->specializations) > 0)
                                <span class="inline-block px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs font-medium mb-2">
                                    {{ $teacher->specializations[0] }}
                                </span>
                            @endif

                            <!-- Experience -->
                            <div class="text-xs text-gray-600 mb-3">
                                <i class="fas fa-clock mr-1"></i>
                                {{ $teacher->experience_text }}
                            </div>

                            <!-- CTA Button -->
                            <a href="/trial-class" class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 rounded text-sm font-semibold transition-colors duration-200">
                                Book Trial
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-br from-blue-600 to-blue-800 text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">
                Ready to Start Learning?
            </h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed font-medium max-w-2xl mx-auto">
                Book a free trial class with any of our expert teachers and experience our teaching quality firsthand.
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/trial-class" class="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200 shadow-lg">
                    <i class="fas fa-play-circle mr-3"></i>
                    Book Free Trial
                </a>
                <a href="/contact" class="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors duration-200">
                    <i class="fas fa-envelope mr-3"></i>
                    Contact Us
                </a>
            </div>
        </div>
    </section>
</x-layouts.app>
