<?php if (isset($component)) { $__componentOriginal5863877a5171c196453bfa0bd807e410 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5863877a5171c196453bfa0bd807e410 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.app','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> Our Courses - Zajel Arabic Academy <?php $__env->endSlot(); ?>
     <?php $__env->slot('metaDescription', null, []); ?> Explore our comprehensive Arabic language and Quran courses designed for non-Arabic speakers. Learn with expert instructors through interactive online classes. <?php $__env->endSlot(); ?>

    <!-- Page Header -->
    <section class="bg-gradient-primary text-white py-16 md:py-20">
        <div class="max-w-7xl mx-auto container-padding">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                    Our Courses
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed font-medium">
                    Discover our comprehensive collection of Arabic language and Quran courses designed to help you achieve your learning goals.
                </p>
            </div>
        </div>
    </section>

    <!-- Filters Section -->
    <section class="bg-white border-b border-gray-200 sticky top-16 md:top-20 z-40">
        <div class="max-w-7xl mx-auto container-padding py-6">
            <form method="GET" class="flex flex-wrap items-center gap-4">
                <!-- Search -->
                <div class="flex-1 min-w-64">
                    <div class="relative">
                        <input 
                            type="text" 
                            name="search" 
                            value="<?php echo e(request('search')); ?>"
                            placeholder="Search courses..." 
                            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium"
                        >
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <!-- Category Filter -->
                <select name="category" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium">
                    <option value="">All Categories</option>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($category->slug); ?>" <?php echo e(request('category') === $category->slug ? 'selected' : ''); ?>>
                            <?php echo e($category->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>

                <!-- Level Filter -->
                <select name="level" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium">
                    <option value="">All Levels</option>
                    <?php $__currentLoopData = $levels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($value); ?>" <?php echo e(request('level') === $value ? 'selected' : ''); ?>>
                            <?php echo e($label); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>

                <!-- Sort -->
                <select name="sort" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-base font-medium">
                    <option value="featured" <?php echo e(request('sort') === 'featured' ? 'selected' : ''); ?>>Featured</option>
                    <option value="newest" <?php echo e(request('sort') === 'newest' ? 'selected' : ''); ?>>Newest</option>
                    <option value="price_low" <?php echo e(request('sort') === 'price_low' ? 'selected' : ''); ?>>Price: Low to High</option>
                    <option value="price_high" <?php echo e(request('sort') === 'price_high' ? 'selected' : ''); ?>>Price: High to Low</option>
                </select>

                <!-- Filter Button -->
                <button type="submit" class="btn btn-primary font-semibold px-6 py-3">
                    <i class="fas fa-filter mr-2"></i>
                    Filter
                </button>

                <!-- Clear Filters -->
                <?php if(request()->hasAny(['search', 'category', 'level', 'sort'])): ?>
                    <a href="/courses" class="btn btn-outline font-semibold px-6 py-3">
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </a>
                <?php endif; ?>
            </form>
        </div>
    </section>

    <!-- Courses Grid -->
    <section class="section-padding bg-gray-50">
        <div class="max-w-7xl mx-auto container-padding">
            <?php if($courses->count() > 0): ?>
                <!-- Results Info -->
                <div class="flex items-center justify-between mb-8">
                    <p class="text-gray-600 font-medium">
                        Showing <?php echo e($courses->firstItem()); ?>-<?php echo e($courses->lastItem()); ?> of <?php echo e($courses->total()); ?> courses
                    </p>
                </div>

                <!-- Courses Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                    <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="card card-hover group">
                            <!-- Course Image -->
                            <div class="relative overflow-hidden">
                                <?php if($course->image): ?>
                                    <img 
                                        src="<?php echo e(Storage::url($course->image)); ?>" 
                                        alt="<?php echo e($course->title); ?>"
                                        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                    >
                                <?php else: ?>
                                    <div class="w-full h-48 bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center">
                                        <i class="fas fa-book-open text-white text-3xl"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Course Level Badge -->
                                <div class="absolute top-4 left-4">
                                    <span class="px-3 py-1 bg-white text-primary-600 text-sm font-semibold rounded-full shadow-md">
                                        <?php echo e(ucfirst($course->level)); ?>

                                    </span>
                                </div>
                                
                                <!-- Course Price -->
                                <?php if($course->price > 0): ?>
                                    <div class="absolute top-4 right-4">
                                        <div class="bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                            <?php if($course->discount_price): ?>
                                                <span class="line-through opacity-75">$<?php echo e(number_format($course->price, 0)); ?></span>
                                                <span class="ml-1">$<?php echo e(number_format($course->discount_price, 0)); ?></span>
                                            <?php else: ?>
                                                $<?php echo e(number_format($course->price, 0)); ?>

                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="absolute top-4 right-4">
                                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                            Free
                                        </span>
                                    </div>
                                <?php endif; ?>

                                <?php if($course->is_featured): ?>
                                    <div class="absolute bottom-4 left-4">
                                        <span class="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold">
                                            <i class="fas fa-star mr-1"></i>
                                            Featured
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Course Content -->
                            <div class="p-6">
                                <!-- Category -->
                                <div class="flex items-center mb-3">
                                    <span class="text-sm font-medium text-primary-600 bg-primary-50 px-3 py-1 rounded-full">
                                        <?php echo e($course->category->name); ?>

                                    </span>
                                </div>

                                <!-- Course Title -->
                                <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-200">
                                    <?php echo e($course->title); ?>

                                </h3>

                                <!-- Course Description -->
                                <p class="text-gray-600 mb-4 leading-relaxed font-medium">
                                    <?php echo e(Str::limit($course->short_description ?: $course->description, 100)); ?>

                                </p>

                                <!-- Course Meta -->
                                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-clock mr-2"></i>
                                        <span class="font-medium"><?php echo e($course->duration ?: '8 weeks'); ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-user mr-2"></i>
                                        <span class="font-medium"><?php echo e($course->instructor_name); ?></span>
                                    </div>
                                </div>

                                <!-- Course Link -->
                                <a href="/courses/<?php echo e($course->slug); ?>" class="block w-full btn btn-primary text-center font-semibold">
                                    <i class="fas fa-arrow-right mr-2"></i>
                                    View Course
                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="flex justify-center">
                    <?php echo e($courses->appends(request()->query())->links()); ?>

                </div>
            <?php else: ?>
                <!-- No Results -->
                <div class="text-center py-16">
                    <i class="fas fa-search text-6xl text-gray-300 mb-6"></i>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">No courses found</h3>
                    <p class="text-lg text-gray-600 mb-8 max-w-md mx-auto">
                        We couldn't find any courses matching your criteria. Try adjusting your filters or search terms.
                    </p>
                    <a href="/courses" class="btn btn-primary font-semibold">
                        <i class="fas fa-refresh mr-2"></i>
                        View All Courses
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section-padding bg-gradient-primary">
        <div class="max-w-4xl mx-auto container-padding text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Can't Find What You're Looking For?
            </h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed font-medium">
                Contact us to discuss your specific learning needs and we'll help you find the perfect course.
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/contact" class="btn bg-white text-primary-600 hover:bg-gray-50 hover:text-primary-700 text-lg font-semibold px-8 py-4 shadow-xl">
                    <i class="fas fa-envelope mr-3"></i>
                    Contact Us
                </a>
                <a href="/trial-class" class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 text-lg font-semibold px-8 py-4">
                    <i class="fas fa-play-circle mr-3"></i>
                    Try Free Class
                </a>
            </div>
        </div>
    </section>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $attributes = $__attributesOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__attributesOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $component = $__componentOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__componentOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\zajelwebsite\resources\views/courses/index.blade.php ENDPATH**/ ?>